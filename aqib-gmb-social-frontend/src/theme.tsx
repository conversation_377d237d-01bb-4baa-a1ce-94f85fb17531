// theme.ts
import { createTheme } from "@mui/material/styles";
import { PaletteColor, PaletteOptions, ThemeOptions } from "@mui/material";

declare module "@mui/material/styles" {
  interface Palette {
    primaryAlpha?: PaletteColor;
    secondaryAlpha?: PaletteColor;
  }

  interface PaletteOptions {
    primaryAlpha?: PaletteOptions["primary"];
    secondaryAlpha?: PaletteOptions["secondary"];
  }
}
const themeOptions: ThemeOptions = {
  // Theme--1
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#00B4E6",
  //         },
  //   secondary: {
  //     main: "#ff6e23",
  //   },
  //   primaryAlpha: {
  //     main: "#00B4E633", // 20% opacity
  //   },
  //   secondaryAlpha: {
  //     main: "#ff6e2333", // 20% opacity
  //   },
  // },

  //  THEME--2
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#00B4E6",
  //         },
  //   secondary: {
  //     main: "#FFC700",
  //   },
  //   primaryAlpha: {
  //     main: "#00B4E633", 
  //   },
  //   secondaryAlpha: {
  //     main: "#FFC70033", 
  //   },
  // },

  //THEME--3
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#0C2A3F",
  //         },
  //   secondary: {
  //     main: "#8ac539",
  //   },
  //   primaryAlpha: {
  //     main: "#0C2A3F33",  
  //   },
  //   secondaryAlpha: {
  //     main: "#FF6E2333", 
  //   },
  // },

  // THEME--4
  palette: {
    mode: "light",
    primary: {
      main: "#309898",
          },
    secondary: {
      main: "#F4631E",
    },
    primaryAlpha: {
      main: "#FF9F0033",  
    },
    secondaryAlpha: {
      main: "#F4631E33", 
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          maxHeight: 30,
        },
      },
    },
  },
};

export const theme = createTheme({ ...themeOptions });
