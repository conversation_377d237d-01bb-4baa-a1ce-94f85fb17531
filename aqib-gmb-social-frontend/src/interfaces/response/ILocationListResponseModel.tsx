import { IPaginationResponseModel } from "../IPaginationResponseModel";

export interface IBusinessListResponseModel {
  message: string;
  list: IBusiness[];
}

export interface IBusiness {
  id: number;
  userId: number;
  businessName: string;
  businessEmail: string;
  businessUrl: any;
  bussinessLogo: any;
  statusId: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  name: string;
  isActive: number;
}

export interface IBusinessListPaginatedResponse {
  message: string;
  pagination: IPaginationResponseModel;
  results: IBusiness[];
}
