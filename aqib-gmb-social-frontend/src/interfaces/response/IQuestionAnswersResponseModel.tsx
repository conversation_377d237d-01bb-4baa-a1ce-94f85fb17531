export interface IQuestionAnswersResponseModel {
  success: boolean;
  message: string;
  list: IQuestionAnswer[];
}

export interface IQuestionAnswer {
  id: number;
  userId: number;
  userName: string;
  gmbAccountId: string;
  gmbLocationId: string;
  gmbQuestionId: string;
  question: string;
  answer: string;
  statusId: number;
  questionCreatedTime: string;
  questionUpdateTime: string;
  createdAt: string;
  updatedAt: string;
}
