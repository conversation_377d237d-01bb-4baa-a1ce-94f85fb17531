import { IPaginationResponseModel } from "../IPaginationResponseModel";

export interface ILocationsListResponseModel {
  message: string;
  list: ILocation[];
}

export interface ILocationsListPaginatedResponseModel {
  message: string;
  pagination: IPaginationResponseModel;
  results: ILocation[];
}

export interface ILocation {
  id: number;
  gmbAccountId: string;
  gmbLocationId: string;
  gmbLocationName: string;
  locality: string;
  postalCode: string;
  businessStream: string;
  statusId: number;
  websiteUri: string;
  description: string;
  placeId: string;
  mapsUri: string;
  newReviewUri: string;
  latitude: string;
  longitude: string;
  primaryPhone: string;
  regularHours?: IRegularHours;
  reviewsStatusId: number;
  proceedStatus?: number;
  lastProcessedOn?: string;
  pickUpDate: any;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  businessId: number;
}

export interface IRegularHours {
  periods: IPeriod[];
}

export interface IPeriod {
  openDay: string;
  closeDay: string;
  openTime: IOpenTime;
  closeTime: ICloseTime;
}

export interface IOpenTime {
  hours?: number;
  minutes?: number;
}

export interface ICloseTime {
  hours: number;
  minutes?: number;
}
