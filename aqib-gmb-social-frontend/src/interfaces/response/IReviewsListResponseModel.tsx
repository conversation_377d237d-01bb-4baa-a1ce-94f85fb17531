export interface IReviewsListResponseModel {
  message: string;
  list: IReviewsResponse[];
  success: boolean;
}

export interface IReviewsResponse {
  id: number;
  locationId: string;
  reviewId: string;
  reviewerName: string;
  reviewerProfilePic: string;
  review?: string;
  starRating: string;
  createTime: string;
  updateTime: string;
  reviewReplyComment: string;
  reviewReplyUpdateTime: string;
  createdAt: string;
  updatedAt: string;
  gmbLocationName: string;
  reviewTags?: string;
  gmbAccountId?: string;
}
