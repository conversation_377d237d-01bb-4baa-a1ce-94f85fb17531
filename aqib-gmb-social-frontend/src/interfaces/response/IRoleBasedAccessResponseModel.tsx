export interface IRoleBasedAccessResponseModel {
  id: number;
  role: string;
  Dashboard: number;
  Analytics: number;
  UserManagement: number;
  UserCreate: number;
  UserEdit: number;
  UserDelete: number;
  BusinessManagement: number;
  BusinessCreate: number;
  BusinessEdit: number;
  BusinessDelete: number;
  CallBack: number;
  LocationManagement: number;
  LocationCreate: number;
  LocationRefresh: number;
  ReviewsManagement: number;
  ReviewsRefresh: number;
  ReviewsReply: number;
  QandAManagement: number;
  QandARefresh: number;
  QandAReply: number;
  RoleManagement: number;
  PostsManagement: number;
  PostsCreate: number;
  PostsEdit: number;
  PostsDelete: number;
  createdAt: string;
  updatedAt: string;
}
