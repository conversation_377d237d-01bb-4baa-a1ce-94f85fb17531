import { CallToAction, Offer, Event } from "./IGoogleCreatePost";

export interface IGoogleCreatePostResponse {
  message: string;
  data: LocalPost[];
  isSuccess: boolean;
}

export interface LocalPost {
  name: string;
  languageCode: string;
  summary: string;
  state: string;
  updateTime: string;
  createTime: string;
  searchUrl: string;
  media: Medum[];
  topicType: string;
  callToAction?: CallToAction | null;
  event?: Event | null;
  offer?: Offer | null;
}

export interface Medum {
  name: string;
  mediaFormat: string;
  googleUrl: string;
}

export interface Schedule {
  startDate: StartDate;
  startTime: StartTime;
  endDate: EndDate;
  endTime: EndTime;
}

export interface StartDate {
  year: number;
  month: number;
  day: number;
}

export interface StartTime {
  hours: number;
}

export interface EndDate {
  year: number;
  month: number;
  day: number;
}

export interface EndTime {
  hours: number;
}
