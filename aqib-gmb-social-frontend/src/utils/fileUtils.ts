/**
 * File utility functions for asset management
 */

export class FileUtils {
  /**
   * Validate file before upload
   * @param file - File to validate
   * @param maxSizeMB - Maximum allowed size in MB
   * @returns Validation result
   */
  static validateFile(file: File, maxSizeMB: number): { valid: boolean; error?: string } {
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: `File size exceeds ${maxSizeMB} MB limit`
      };
    }

    // Check file type
    const allowedTypes = [
      // Images
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      // Videos
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'
    ];

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Unsupported file type: ${file.type}`
      };
    }

    return { valid: true };
  }

  /**
   * Format file size for display
   * @param bytes - File size in bytes
   * @returns Formatted file size string
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if file is an image
   * @param mimeType - File MIME type
   * @returns True if file is an image
   */
  static isImage(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Check if file is a video
   * @param mimeType - File MIME type
   * @returns True if file is a video
   */
  static isVideo(mimeType: string): boolean {
    return mimeType.startsWith('video/');
  }

  /**
   * Get file type display name
   * @param mimeType - File MIME type
   * @returns Display name for file type
   */
  static getFileTypeDisplay(mimeType: string): string {
    if (this.isImage(mimeType)) return 'Image';
    if (this.isVideo(mimeType)) return 'Video';
    return 'File';
  }

  /**
   * Generate thumbnail URL for video files
   * @param videoUrl - Video URL
   * @returns Thumbnail URL (placeholder for now)
   */
  static getVideoThumbnail(videoUrl: string): string {
    // For now, return a placeholder. In production, you might want to generate
    // actual thumbnails or use a service like AWS Lambda to create them
    return "/assets/video-placeholder.png";
  }
}
