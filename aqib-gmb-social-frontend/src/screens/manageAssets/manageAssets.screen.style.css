/* Manage Assets Screen Styles */

.manage-assets-container {
  padding: 20px;
}

.storage-info-card {
  margin-bottom: 24px;
}

.storage-progress {
  height: 10px;
  border-radius: 5px;
  margin-bottom: 8px;
}

.upload-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.upload-zone:hover {
  border-color: #1976d2;
  background-color: #f5f5f5;
}

.upload-zone.dragover {
  border-color: #1976d2;
  background-color: #e3f2fd;
}

.upload-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px !important;
  color: #1976d2;
}

.upload-text {
  color: #666;
  margin: 0;
}

.upload-button {
  margin-top: 16px !important;
}

.assets-grid {
  margin-top: 24px;
}

.asset-card {
  position: relative;
  height: 200px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden;
}

.asset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.asset-card-media {
  width: 100%;
  height: 140px;
  object-fit: cover;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.asset-card-media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.asset-card-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.asset-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}

.asset-placeholder-icon {
  font-size: 48px !important;
  margin-bottom: 8px;
}

.asset-card-content {
  padding: 8px 12px !important;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.asset-filename {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.asset-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.asset-size {
  font-size: 0.75rem;
  color: #666;
}

.asset-type-chip {
  font-size: 0.625rem !important;
  height: 20px !important;
}

.asset-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.asset-card:hover .asset-actions {
  opacity: 1;
}

.asset-action-button {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  min-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
}

.asset-action-button:hover {
  background-color: rgba(255, 255, 255, 1) !important;
}

.delete-button {
  color: #d32f2f !important;
}

.delete-button:hover {
  background-color: rgba(211, 47, 47, 0.1) !important;
}

.no-assets {
  text-align: center;
  padding: 40px;
  color: #666;
}

.no-assets-icon {
  font-size: 64px !important;
  color: #ccc;
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.business-selection {
  margin-bottom: 24px;
}

.business-button {
  margin-right: 12px !important;
  margin-bottom: 8px !important;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.file-input {
  display: none;
}

.upload-progress {
  margin-top: 16px;
}

.storage-warning {
  margin-bottom: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .asset-card {
    height: 180px;
  }
  
  .asset-card-media {
    height: 120px;
  }
  
  .upload-zone {
    padding: 24px;
  }
  
  .upload-icon {
    font-size: 36px !important;
  }
}

@media (max-width: 480px) {
  .asset-card {
    height: 160px;
  }
  
  .asset-card-media {
    height: 100px;
  }
  
  .upload-zone {
    padding: 16px;
  }
  
  .upload-icon {
    font-size: 32px !important;
  }
  
  .asset-filename {
    font-size: 0.75rem;
  }
}
