import { FunctionComponent } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Link from "@mui/material/Link";
import Button from "@mui/material/Button";
import { Typography } from "@mui/material";
import * as yup from "yup";

const ForgotPassword: FunctionComponent<PageProps> = ({ title }) => {
  return (
    <div className="height100">
      <Box className="height100">
        <Grid container spacing={2} className="height100">
          <Grid item xs={12} md={6} lg={6}>
            <Box className="accountLeft">
              <img
                alt="MyLocoBiz - Login"
                className="width100"
                src={require("../../assets/login/loginLeftSlider.png")}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={6} lg={6}>
            <Box className="accountRight">
              <Box className="accountTopPart">
                <Typography className="commonTitle">Welcome to</Typography>
                <Box className="accountLogo">
                  <img
                    alt="MyLocoBiz - Logo"
                    className="width100"
                    src={require("../../assets/common/Logo.png")}
                  />
                </Box>
              </Box>
              <Box className="accountBodyPart">
                <form>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={12} lg={12}>
                      <Box className="commonInput">
                        <TextField
                          id="filled-basic"
                          className="width100"
                          label="Email"
                          type="email"
                          variant="filled"
                        />
                      </Box>
                    </Grid>

                    <Grid xs={6} md={6} lg={6}>
                      <Link href="/" className="floatR">
                        Continue with Login.
                      </Link>
                    </Grid>
                    <Grid xs={12} md={12} lg={12}>
                      <Button variant="contained" className="primaryFillBtn">
                        Reset My Password
                      </Button>
                    </Grid>
                  </Grid>
                </form>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default ForgotPassword;
