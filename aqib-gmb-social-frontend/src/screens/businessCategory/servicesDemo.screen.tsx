import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Button,
  Typography,
  Card,
  CardContent,
  Divider,
  IconButton,
  Dialog,
} from "@mui/material";
import AddServicesModal from "./components/addServices.component";
import AddIcon from "@mui/icons-material/Add";

interface ServicesDemoScreenProps {
  title?: string;
}

const ServicesDemoScreen: React.FC<ServicesDemoScreenProps> = ({ title }) => {
  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);

  const [isAddServicesModalOpen, setIsAddServicesModalOpen] = useState(false);
  const [services, setServices] = useState<string[]>([]);

  const handleOpenAddServicesModal = () => {
    setIsAddServicesModalOpen(true);
  };

  const handleCloseAddServicesModal = () => {
    setIsAddServicesModalOpen(false);
  };

  const handleAddService = (serviceName: string) => {
    setServices([...services, serviceName]);
    setIsAddServicesModalOpen(false);
  };

  return (
    <Box sx={{ p: 3, maxWidth: "800px", margin: "0 auto" }}>
      <Typography variant="h4" gutterBottom>
        Add Services Demo
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        This is a demo page to showcase the add services modal
      </Typography>

      <Card sx={{ mb: 4, bgcolor: "white" }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Demo Controls
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenAddServicesModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Open Add Services Modal
            </Button>
          </Box>

          {services.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Added Services:
              </Typography>
              <ul>
                {services.map((service, index) => (
                  <li key={index}>
                    <Typography>{service}</Typography>
                  </li>
                ))}
              </ul>
            </Box>
          )}
        </CardContent>
      </Card>

      <AddServicesModal
        open={isAddServicesModalOpen}
        onClose={handleCloseAddServicesModal}
        categoryName="Ophthalmologist"
        isPrimary={false}
        onAddService={handleAddService}
      />
    </Box>
  );
};

export default ServicesDemoScreen;
