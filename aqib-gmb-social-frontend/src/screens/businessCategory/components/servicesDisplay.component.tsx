import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Di<PERSON><PERSON>,
  Button,
  IconButton,
  Alert,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import AddServicesModal from "./addServices.component";

interface Service {
  name: string;
  description: string;
}

interface Category {
  name: string;
  isPrimary: boolean;
  services: Service[];
}

interface ServicesDisplayProps {
  categories: Category[];
  onUpdateCategories?: (updatedCategories: Category[]) => void;
}

const ServicesDisplay: React.FC<ServicesDisplayProps> = ({
  categories,
  onUpdateCategories,
}) => {
  const [addServicesModalOpen, setAddServicesModalOpen] = useState(false);
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState<
    number | null
  >(null);

  const handleOpenAddServicesModal = (categoryIndex: number) => {
    setSelectedCategoryIndex(categoryIndex);
    setAddServicesModalOpen(true);
  };

  const handleCloseAddServicesModal = () => {
    setAddServicesModalOpen(false);
    setSelectedCategoryIndex(null);
  };

  const handleAddService = (serviceName: string) => {
    if (selectedCategoryIndex !== null && onUpdateCategories) {
      const updatedCategories = [...categories];
      updatedCategories[selectedCategoryIndex].services.push({
        name: serviceName,
        description: "",
      });
      onUpdateCategories(updatedCategories);
    }
    handleCloseAddServicesModal();
  };

  const handleDeleteCategory = (categoryIndex: number) => {
    if (onUpdateCategories) {
      const updatedCategories = categories.filter(
        (_, index) => index !== categoryIndex
      );
      onUpdateCategories(updatedCategories);
    }
  };

  return (
    <Box>
      {/* Pending review alert */}
      <Alert
        severity="warning"
        icon={<InfoOutlinedIcon />}
        sx={{
          mb: 3,
          bgcolor: "rgba(255, 152, 0, 0.1)",
          color: "text.primary",
          "& .MuiAlert-icon": {
            color: "warning.main",
          },
          "& .MuiAlert-message": {
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
          },
          px: { xs: 1, sm: 2 },
          py: { xs: 0.5, sm: 1 },
        }}
        action={
          <Button
            color="primary"
            size="small"
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.8125rem" },
              p: { xs: "2px 4px", sm: "3px 9px" },
            }}
          >
            Learn more
          </Button>
        }
      >
        Your edit is pending review. It may take up to one day to be published.
      </Alert>

      {categories.map((category, categoryIndex) => (
        <Box key={categoryIndex} sx={{ mb: { xs: 3, sm: 4 } }}>
          <Box sx={{ mb: 1 }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: { xs: "1rem", sm: "1.25rem" },
                wordBreak: "break-word",
              }}
            >
              {category.name}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                display: "block",
                fontSize: { xs: "0.7rem", sm: "0.75rem" },
              }}
            >
              {category.isPrimary ? "Primary category" : "Additional category"}
            </Typography>
          </Box>

          <Divider sx={{ my: 1 }} />

          {category.services.map((service, serviceIndex) => (
            <Box
              key={serviceIndex}
              sx={{
                py: { xs: 1.5, sm: 2 },
                px: { xs: 0.5, sm: 1 },
                display: "flex",
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
                flexWrap: { xs: "wrap", sm: "nowrap" },
              }}
            >
              <Box
                sx={{
                  width: { xs: "calc(100% - 40px)", sm: "auto" },
                  flexGrow: 1,
                  pr: { xs: 1, sm: 2 },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontSize: { xs: "0.9rem", sm: "1rem" },
                    wordBreak: "break-word",
                  }}
                >
                  {service.name}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: 1,
                    WebkitBoxOrient: "vertical",
                  }}
                >
                  {service.description}
                </Typography>
              </Box>
              <IconButton
                sx={{
                  p: { xs: 0.5, sm: 1 },
                  alignSelf: { xs: "center", sm: "auto" },
                }}
              >
                <ChevronRightIcon
                  sx={{
                    fontSize: { xs: "1.25rem", sm: "1.5rem" },
                  }}
                />
              </IconButton>
            </Box>
          ))}

          <Button
            startIcon={<AddIcon />}
            onClick={() => handleOpenAddServicesModal(categoryIndex)}
            sx={{
              mt: 2,
              color: "primary.main",
              textTransform: "none",
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              px: { xs: 1, sm: 2 },
              "& .MuiButton-startIcon": {
                mr: { xs: 0.5, sm: 1 },
              },
            }}
          >
            Add more services
          </Button>

          {!category.isPrimary && (
            <Box
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Button
                startIcon={<DeleteOutlineIcon />}
                color="error"
                onClick={() => handleDeleteCategory(categoryIndex)}
                sx={{
                  textTransform: "none",
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  "& .MuiButton-startIcon": {
                    mr: { xs: 0.5, sm: 1 },
                  },
                }}
              >
                Delete
              </Button>
            </Box>
          )}

          {categoryIndex < categories.length - 1 && (
            <Divider sx={{ my: { xs: 2, sm: 3 } }} />
          )}
        </Box>
      ))}

      {/* Add Services Modal */}
      {selectedCategoryIndex !== null && (
        <AddServicesModal
          open={addServicesModalOpen}
          onClose={handleCloseAddServicesModal}
          categoryName={categories[selectedCategoryIndex]?.name || ""}
          isPrimary={categories[selectedCategoryIndex]?.isPrimary || false}
          onAddService={handleAddService}
        />
      )}
    </Box>
  );
};

export default ServicesDisplay;
