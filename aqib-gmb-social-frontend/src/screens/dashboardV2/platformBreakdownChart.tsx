import React, { useEffect, useState } from "react";
import { Doughn<PERSON> } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Divider, Typography } from "@mui/material";
import { theme } from "../../theme";

import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend,
  ArcElement,
  ChartOptions,
} from "chart.js";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  ArcElement,
  Tooltip,
  Legend
);

interface DatedValue {
  date: {
    year: number;
    month: number;
    day: number;
  };
  value?: string;
}

interface TimeSeries {
  datedValues: DatedValue[];
}

interface DailyMetricTimeSeries {
  dailyMetric: string;
  timeSeries: TimeSeries;
}

interface MultiDailyMetricTimeSeries {
  dailyMetricTimeSeries: DailyMetricTimeSeries[];
}

interface RootObject {
  multiDailyMetricTimeSeries: MultiDailyMetricTimeSeries[];
}

interface PlatformDataInput {
  label: string;
  value: number;
}

interface Props {
  data: RootObject;
}

const PlatformBreakdownChart: React.FC<Props> = ({ data }) => {
  const [platformData, setPlatformData] = useState<PlatformDataInput[]>([]);

  useEffect(() => {
    if (
      data &&
      data.multiDailyMetricTimeSeries &&
      data.multiDailyMetricTimeSeries[0]
    ) {
      const series = data.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries;
      const result = series
        .filter((x) => x.dailyMetric.includes("BUSINESS_IMPRESSIONS"))
        .map((metric) => {
          const label = metric.dailyMetric
            .replace("BUSINESS_IMPRESSIONS_", "")
            .replace(/_/g, " ")
            .toLowerCase()
            .replace(/(^\w|\s\w)/g, (m) => m.toUpperCase());

          const value =
            metric.timeSeries.datedValues &&
            metric.timeSeries.datedValues.reduce((acc, curr) => {
              return acc + (curr.value ? parseInt(curr.value) : 0);
            }, 0);

          return { label, value };
        });

      setPlatformData(result);
    } else {
      setPlatformData([]);
    }
  }, [data]);

  const total = platformData.reduce((sum, entry) => sum + entry.value, 0);

  const chartData = {
    labels: platformData.map(
      (entry) => `${entry.label} (${Math.round((entry.value / total) * 100)}%)`
    ),
    datasets: [
      {
        data: platformData.map((entry) => entry.value),
        backgroundColor: [
          theme.palette.primary?.main,
          "#66bb6a",
          "#ffa726",
          theme.palette.secondary?.main,
        ],
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    plugins: {
      legend: {
        position: "right",
        labels: {
          padding: 20,
        },
      },
      datalabels: {
        color: "#fff",
        formatter: (value: number, context: any) => {
          // const total = context.chart.data.datasets[0].data.reduce(
          //   (sum: number, val: number) => sum + val,
          //   0
          // );

          return `${value}`;
        },
        font: {
          weight: "lighter",
          size: 12,
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Platform Breakdown
      </Typography>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        {total}
      </Typography>
      <Typography variant="subtitle2" sx={{ mb: 2 }}>
        People viewed your Business Profile
      </Typography>
      <div style={{ height: "300px", position: "relative" }}>
        <Doughnut
          data={chartData}
          options={options}
          plugins={[ChartDataLabels]}
        />
      </div>
    </div>
  );
};

export default PlatformBreakdownChart;
