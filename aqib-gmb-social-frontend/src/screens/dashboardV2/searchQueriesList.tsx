import React from "react";

import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale, // 👈 You need this
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale, // 👈 Register it here too
  ArcE<PERSON>,
  Toolt<PERSON>,
  Legend
);

interface Props {
  data: any;
}

const SearchQueriesList: React.FC<Props> = ({ data }) => {
  const queries = [
    { term: "eye hospital", count: 120 },
    { term: "optometrist", count: 85 },
    { term: "best eye clinic", count: 60 },
  ]; // You can use data.searchTerms or similar

  return (
    <ul style={{ listStyle: "none", padding: 0 }}>
      {queries.map((q, idx) => (
        <li key={idx}>
          <strong>{idx + 1}.</strong> {q.term} — {q.count}
        </li>
      ))}
    </ul>
  );
};

export default SearchQueriesList;
