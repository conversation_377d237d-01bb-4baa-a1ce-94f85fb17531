import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Stack,
  Rating,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../../context/loading.context";
import { ToastContext } from "../../../../context/toast.context";
import { ToastSeverity } from "../../../../constants/toastSeverity.constant";
import ReviewSettingsService, {
  IReplyTemplate,
  ICreateReplyTemplateRequest,
  IUpdateReplyTemplateRequest,
} from "../../../../services/reviewSettings/reviewSettings.service";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

interface ICreateEditTemplateProps {
  template: IReplyTemplate | null;
  businessId: number | null;
  onClose: () => void;
}

const validationSchema = yup.object({
  templateName: yup
    .string()
    .required("Template name is required")
    .min(3, "Template name must be at least 3 characters")
    .max(255, "Template name must be less than 255 characters"),
  templateContent: yup
    .string()
    .required("Template content is required")
    .min(10, "Template content must be at least 10 characters")
    .max(1000, "Template content must be less than 1000 characters"),
  starRating: yup
    .number()
    .required("Star rating is required")
    .min(1, "Star rating must be between 1 and 5")
    .max(5, "Star rating must be between 1 and 5"),
  isDefault: yup.boolean(),
});

const CreateEditTemplateComponent: React.FunctionComponent<
  ICreateEditTemplateProps
> = ({ template, businessId, onClose }) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const { userInfo } = useSelector((state: any) => state.authReducer);

  const _reviewSettingsService = new ReviewSettingsService(dispatch);

  const initialValues = {
    templateName: template?.template_name || "",
    templateContent: template?.template_content || "",
    starRating: template?.star_rating || 5,
    isDefault: template?.is_default || false,
  };

  const handleSubmit = async (values: any) => {
    if (!userInfo?.id) {
      setToastConfig(ToastSeverity.Error, "User not authenticated", true);
      return;
    }

    try {
      setLoading(true);

      if (template?.id) {
        // Update existing template
        const updateData: IUpdateReplyTemplateRequest = {
          starRating: values.starRating,
          templateName: values.templateName,
          templateContent: values.templateContent,
          isDefault: values.isDefault,
        };

        await _reviewSettingsService.updateReplyTemplate(
          userInfo.id,
          template.id,
          updateData
        );

        setToastConfig(
          ToastSeverity.Success,
          "Template updated successfully",
          true
        );
      } else {
        // Create new template
        const createData: ICreateReplyTemplateRequest = {
          starRating: values.starRating,
          templateName: values.templateName,
          templateContent: values.templateContent,
          isDefault: values.isDefault,
          businessId: businessId || undefined,
        };

        await _reviewSettingsService.createReplyTemplate(
          userInfo.id,
          createData
        );

        setToastConfig(
          ToastSeverity.Success,
          "Template created successfully",
          true
        );
      }

      onClose();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        template?.id
          ? "Failed to update template"
          : "Failed to create template",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const getPreviewText = (content: string, rating: number) => {
    // Replace common placeholders with sample data
    return content
      .replace(/\{customerName\}/g, "John Doe")
      .replace(/\{businessName\}/g, "Your Business")
      .replace(/\{rating\}/g, rating.toString());
  };

  return (
    <Box className="height100" sx={{ p: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        {template?.id ? "Edit" : "Create"} Reply Template
      </Typography>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          setFieldValue,
        }) => (
          <Form id="template-form">
            <Stack spacing={3}>
              <TextField
                fullWidth
                name="templateName"
                label="Template Name"
                value={values.templateName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.templateName && Boolean(errors.templateName)}
                helperText={touched.templateName && errors.templateName}
                placeholder="e.g., Thank You - 5 Star"
              />

              {template?.id ? (
                // Read-only star rating display for editing
                <Box>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 1 }}
                  >
                    Star Rating
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      p: 2,
                      border: "1px solid",
                      borderColor: "divider",
                      borderRadius: 1,
                      backgroundColor: "grey.50",
                    }}
                  >
                    <Rating
                      value={values.starRating}
                      readOnly
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body1">
                      {values.starRating} Star
                      {values.starRating !== 1 ? "s" : ""}
                    </Typography>
                  </Box>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ mt: 0.5 }}
                  >
                    Star rating cannot be changed when editing a template
                  </Typography>
                </Box>
              ) : (
                // Editable star rating selection for creating new template
                <FormControl fullWidth>
                  <InputLabel>Star Rating</InputLabel>
                  <Select
                    name="starRating"
                    value={values.starRating}
                    label="Star Rating"
                    onChange={(e) =>
                      setFieldValue("starRating", e.target.value)
                    }
                    error={touched.starRating && Boolean(errors.starRating)}
                  >
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <MenuItem key={rating} value={rating}>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Rating
                            value={rating}
                            readOnly
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          {rating} Star{rating !== 1 ? "s" : ""}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              <TextField
                fullWidth
                multiline
                rows={6}
                name="templateContent"
                label="Template Content"
                value={values.templateContent}
                onChange={handleChange}
                onBlur={handleBlur}
                error={
                  touched.templateContent && Boolean(errors.templateContent)
                }
                helperText={
                  (touched.templateContent && errors.templateContent) ||
                  "You can use placeholders like {customerName}, {businessName}, {rating}"
                }
                placeholder="Thank you for your review! We appreciate your feedback..."
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={values.isDefault}
                    onChange={(e) =>
                      setFieldValue("isDefault", e.target.checked)
                    }
                    name="isDefault"
                  />
                }
                label="Set as default template for this star rating"
              />

              {values.templateContent && (
                <>
                  <Divider />
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Preview
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 2 }}
                      >
                        <Rating
                          value={values.starRating}
                          readOnly
                          size="small"
                        />
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {values.starRating} Star Review Response
                        </Typography>
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{ fontStyle: "italic", color: "text.secondary" }}
                      >
                        {getPreviewText(
                          values.templateContent,
                          values.starRating
                        )}
                      </Typography>
                    </CardContent>
                  </Card>
                </>
              )}
            </Stack>
          </Form>
        )}
      </Formik>

      <Box className="">
        <Stack direction="row" className="commonFooter">
          <Button
            variant="outlined"
            className="tableActionBtn"
            onClick={onClose}
            sx={{
              minHeight: "50px",
            }}
            startIcon={<CancelOutlinedIcon />}
          >
            <span className="responsiveHide">Cancel</span>
          </Button>
          <Button
            type="submit"
            variant="contained"
            className="tableActionBtn"
            form="template-form"
            sx={{
              minHeight: "50px",
            }}
            startIcon={
              template?.id ? <EditOutlinedIcon /> : <AddOutlinedIcon />
            }
          >
            <span className="responsiveHide">
              {template?.id ? "Update" : "Create"} Template
            </span>
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default CreateEditTemplateComponent;
