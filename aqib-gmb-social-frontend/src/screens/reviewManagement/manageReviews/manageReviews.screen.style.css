:root {
    --postPreviewBgColor: #5D5DFF
}

.reviewCard {
    flex-direction: column;
}

.reviewContent {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    height: 48px;
    color: var(--reviewDescription);
}

.reviewContentDefault {
    height: 48px;
    font-weight: 600;
    font-style: italic;
    color: #b5b5b5;
}

.postPreviewEdit {
    height: 100%;
}

.postPreviewEdit .MuiTabs-root {
    height: 48px;
    margin-left: 20px;
    margin-bottom: 10px;
}

.postPreviewEdit .MuiTabs-root+.MuiBox-root {
    height: calc(100% - 164px);
    overflow-y: visible;
}

.bindedTags {
    overflow-x: scroll;
    display: flex;
    white-space: nowrap;
    padding: 0px 0px 12px;
}

.bindedTags .tagChips {
    margin-right: 4px !important;
}


/* .postPreview
{
    background-color: var(--postPreviewBgColor);
    padding: 42px 20px 20px;
} */