import React, {
  ChangeEvent,
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import Switch from "@mui/material/Switch";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import DeleteIcon from "@mui/icons-material/Delete";
import SendIcon from "@mui/icons-material/Send";
import Stack from "@mui/material/Stack";

//Icons
import DriveFileRenameOutlineIcon from "@mui/icons-material/DriveFileRenameOutline";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";

//Components
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";

//Css
import "../roles/roles.screen.style.css";
import RolesService from "../../../services/roles/roles.service";
import { useDispatch, useSelector } from "react-redux";
import {
  IRole,
  IRolesResponseModel,
} from "../../../interfaces/response/IRolesResponseModel";
import { ObjectType } from "typescript";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { Drawer } from "@mui/material";
import { LoadingContext } from "../../../context/loading.context";
import { MessageConstants } from "../../../constants/message.constant";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import { RoleType } from "../../../constants/dbConstant.constant";
import { useNavigate } from "react-router-dom";
import { theme } from "../../../theme";

type IAddEditRole = {
  show: boolean;
  role: IRole | null;
};

const Roles: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [openEdit, setOpenEdit] = React.useState<IAddEditRole>({
    show: false,
    role: null,
  });

  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [rolesList, setRolesList] = useState<IRole[]>([]);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const _rolesService = new RolesService(dispatch);

  const getRolesList = async () => {
    try {
      setLoading(true);
      var roles: IRolesResponseModel = await _rolesService.roleList(
        userInfo.id
      );
      if (roles.list.length > 0) {
        setRolesList(roles.list);
        if (openEdit.show && openEdit.role != null) {
          setOpenEdit({
            role: roles.list.filter((x) => x.id === openEdit.role?.id)[0],
            show: true,
          });
        }
      }
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    }

    setLoading(false);
  };

  useEffect(() => {
    if (userInfo.roleId !== RoleType.Admin) {
      navigate("/un-authorized");
    }
  }, []);

  useEffect(() => {
    getRolesList();
  }, []);

  const featuresNameText = (role: IRole) => {
    let rolesFeaturesString = "";
    const someObj: ObjectType = role as any;
    const validationKeys = [
      "BusinessManagement",
      "LocationManagement",
      "PostsManagement",
      "QandAManagement",
      "ReviewsManagement",
      "RoleManagement",
      "UserManagement",
    ];
    const orderedKeys = Object.keys(role)
      .sort()
      .reduce((obj: any, key) => {
        obj[key as keyof ObjectType] = someObj[key as keyof ObjectType];
        return obj;
      }, {});
    Object.keys(orderedKeys).forEach(function (key: string, index: number) {
      if (
        validationKeys.includes(key) &&
        someObj[key as keyof ObjectType] === 1
      ) {
        switch (key) {
          case "Analytics":
            rolesFeaturesString += ", Analytics";
            break;
          case "BusinessCreate":
            rolesFeaturesString += ", Business Create";
            break;
          case "BusinessDelete":
            rolesFeaturesString += ", Business Delete";
            break;
          case "BusinessEdit":
            rolesFeaturesString += ", Business Edit";
            break;
          case "BusinessManagement":
            rolesFeaturesString += ", Business Management";
            break;
          case "CallBack":
            rolesFeaturesString += ", CallBack";
            break;
          case "Dashboard":
            rolesFeaturesString += ", Dashboard";
            break;
          case "LocationCreate":
            rolesFeaturesString += ", Location Create";
            break;
          case "LocationManagement":
            rolesFeaturesString += ", Location Management";
            break;
          case "LocationRefresh":
            rolesFeaturesString += ", Location Refresh";
            break;
          case "PostsCreate":
            rolesFeaturesString += ", Posts Create";
            break;
          case "PostsDelete":
            rolesFeaturesString += ", Posts Delete";
            break;
          case "PostsEdit":
            rolesFeaturesString += ", Posts Edit";
            break;
          case "PostsManagement":
            rolesFeaturesString += ", Posts Management";
            break;
          case "QandAManagement":
            rolesFeaturesString += ", Q&A Management";
            break;
          case "QandARefresh":
            rolesFeaturesString += ", QandA Refresh";
            break;
          case "QandAReply":
            rolesFeaturesString += ", QandA Reply";
            break;
          case "ReviewsManagement":
            rolesFeaturesString += ", Reviews Management";
            break;
          case "ReviewsRefresh":
            rolesFeaturesString += ", Reviews Refresh";
            break;
          case "ReviewsReply":
            rolesFeaturesString += ", Reviews Reply";
            break;
          case "RoleManagement":
            rolesFeaturesString += ", Role Management";
            break;
          case "UserCreate":
            rolesFeaturesString += ", User Create";
            break;
          case "UserDelete":
            rolesFeaturesString += ", User Delete";
            break;
          case "UserEdit":
            rolesFeaturesString += ", User Edit";
            break;
          case "UserManagement":
            rolesFeaturesString += ", User Management";
            break;
        }
      }
    });

    return rolesFeaturesString.substring(1);
  };

  const updateRole = async (
    roleId: number | undefined,
    fieldname: string,
    value: number
  ) => {
    setLoading(true);
    try {
      const response = await _rolesService.updateRole(roleId, {
        fieldname,
        value,
      });
      setToastConfig(ToastSeverity.Success, "Updated Successfully.", true);

      getRolesList();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    }

    setLoading(false);
  };

  const getIntegerFromBoolean = (value: boolean) => (value ? 1 : 0);

  const getClassNameOnRoleId = (roleId: number) => {
    switch (roleId) {
      case RoleType.Admin:
        return "hightlightBox admin";
      case RoleType.Manager:
        return "hightlightBox manager";
      case RoleType.User:
        return "hightlightBox user";
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              <Box className="commonTableHeader">
                <h3 className="pageTitle">Role Management</h3>
              </Box>
              <Box>
                <TableContainer className="commonTable">
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Names/Permissions</TableCell>
                        <TableCell align="right">Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {rolesList.length > 0 &&
                        rolesList.map((role: IRole) => (
                          <TableRow>
                            <TableCell
                              scope="row"
                              className="gridResponsiveTextLeft"
                            >
                              <Typography className="tablePrimaryText hightlightBoxText">
                                <span
                                  className={getClassNameOnRoleId(role.id)}
                                ></span>
                                {role.role}
                              </Typography>
                              <Typography className="tableSecondaryText">
                                {featuresNameText(role)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right" data-label="Status">
                              <FormControlLabel
                                control={
                                  <Switch
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) => {
                                      updateRole(
                                        role.id,
                                        "IsActive",
                                        getIntegerFromBoolean(checked)
                                      );
                                    }}
                                    checked={Boolean(role.IsActive)}
                                  />
                                }
                                label=""
                              />
                            </TableCell>
                            <TableCell align="right" data-label="Actions">
                              <Box className="commonTableActionBtns">
                                <Box>
                                  <Button
                                    variant="outlined"
                                    className="emptyBtn editIconBtn"
                                    startIcon={<DriveFileRenameOutlineIcon />}
                                    onClick={() =>
                                      setOpenEdit({ show: true, role })
                                    }
                                  ></Button>
                                </Box>
                                {/* <Box>
                                  <Button
                                    variant="outlined"
                                    className="emptyBtn"
                                    startIcon={<DeleteOutlineIcon />}
                                  >
                                    Delete
                                  </Button>
                                </Box> */}
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>

      <GenericDrawer
        component={
          <Box className="commonModal">
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              <span>{openEdit && openEdit.role && openEdit.role.role}</span>
              <span className="modal-sub-title">Permissions</span>
            </Typography>
            {openEdit && openEdit.role != null && (
              <Box
                id="modal-modal-description"
                className="modal-modal-description"
              >
                <Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.UserManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "UserCreate",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "UserEdit",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "UserDelete",
                                getIntegerFromBoolean(checked)
                              );
                            }
                            updateRole(
                              openEdit.role?.id,
                              "UserManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="User Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.UserManagement)}
                            checked={Boolean(openEdit.role.UserCreate)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "UserCreate",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Create"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.UserManagement)}
                            checked={Boolean(openEdit.role.UserEdit)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "UserEdit",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Edit"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.UserManagement)}
                            checked={Boolean(openEdit.role.UserDelete)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "UserDelete",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Delete"
                      />
                    </FormGroup>
                  </Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.BusinessManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "BusinessCreate",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "BusinessEdit",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "BusinessDelete",
                                getIntegerFromBoolean(checked)
                              );
                            }

                            updateRole(
                              openEdit.role?.id,
                              "BusinessManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="Business Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            defaultChecked
                            disabled={
                              !Boolean(openEdit.role?.BusinessManagement)
                            }
                            checked={Boolean(openEdit.role.BusinessCreate)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "BusinessCreate",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Create"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={
                              !Boolean(openEdit.role?.BusinessManagement)
                            }
                            checked={Boolean(openEdit.role.BusinessEdit)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "BusinessEdit",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Edit"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={
                              !Boolean(openEdit.role?.BusinessManagement)
                            }
                            checked={Boolean(openEdit.role.BusinessDelete)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "BusinessDelete",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Delete"
                      />
                    </FormGroup>
                  </Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.LocationManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "LocationCreate",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "LocationRefresh",
                                getIntegerFromBoolean(checked)
                              );
                            }

                            updateRole(
                              openEdit.role?.id,
                              "LocationManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="Location Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            defaultChecked
                            disabled={
                              !Boolean(openEdit.role?.LocationManagement)
                            }
                            checked={Boolean(openEdit.role.LocationCreate)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "LocationCreate",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Create"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={
                              !Boolean(openEdit.role?.LocationManagement)
                            }
                            checked={Boolean(openEdit.role.LocationRefresh)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "LocationRefresh",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Refresh"
                      />
                    </FormGroup>
                  </Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.ReviewsManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "ReviewsRefresh",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "ReviewsReply",
                                getIntegerFromBoolean(checked)
                              );
                            }

                            updateRole(
                              openEdit.role?.id,
                              "ReviewsManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="Reviews Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            disabled={
                              !Boolean(openEdit.role?.ReviewsManagement)
                            }
                            checked={Boolean(openEdit.role?.ReviewsRefresh)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "ReviewsRefresh",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Refresh"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={
                              !Boolean(openEdit.role?.ReviewsManagement)
                            }
                            checked={Boolean(openEdit.role?.ReviewsReply)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "ReviewsReply",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Reply"
                      />
                    </FormGroup>
                  </Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.QandAManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "QandARefresh",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "QandAReply",
                                getIntegerFromBoolean(checked)
                              );
                            }

                            updateRole(
                              openEdit.role?.id,
                              "QandAManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="Q&A Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.QandAManagement)}
                            checked={Boolean(openEdit.role?.QandARefresh)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "QandARefresh",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Refresh"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.QandAManagement)}
                            checked={Boolean(openEdit.role?.QandAReply)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "QandAReply",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Reply"
                      />
                    </FormGroup>
                  </Box>
                  <Box className="commonRoleInput">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(openEdit.role?.PostsManagement)}
                          onChange={(
                            event: ChangeEvent<HTMLInputElement>,
                            checked: boolean
                          ) => {
                            if (!checked) {
                              updateRole(
                                openEdit.role?.id,
                                "PostsCreate",
                                getIntegerFromBoolean(checked)
                              );
                              updateRole(
                                openEdit.role?.id,
                                "PostsEdit",
                                getIntegerFromBoolean(checked)
                              );

                              updateRole(
                                openEdit.role?.id,
                                "PostsDelete",
                                getIntegerFromBoolean(checked)
                              );
                            }

                            updateRole(
                              openEdit.role?.id,
                              "PostsManagement",
                              getIntegerFromBoolean(checked)
                            );
                          }}
                          color={"default"}
                        />
                      }
                      label="Posts Management"
                    />
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.PostsManagement)}
                            checked={Boolean(openEdit.role?.PostsCreate)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "PostsCreate",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Create"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.PostsManagement)}
                            checked={Boolean(openEdit.role?.PostsEdit)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "PostsEdit",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Edit"
                      />
                      <FormControlLabel
                        required
                        control={
                          <Checkbox
                            disabled={!Boolean(openEdit.role?.PostsManagement)}
                            checked={Boolean(openEdit.role?.PostsDelete)}
                            onChange={(
                              event: ChangeEvent<HTMLInputElement>,
                              checked: boolean
                            ) =>
                              updateRole(
                                openEdit.role?.id,
                                "PostsDelete",
                                getIntegerFromBoolean(checked)
                              )
                            }
                          />
                        }
                        label="Delete"
                      />
                    </FormGroup>
                  </Box>
                </Box>
              </Box>
            )}

            <Box>
              <Stack direction="row" className="commonFooter">
                <Button
                  variant="outlined"
                  className="secondaryOutlineBtn"
                  onClick={() => setOpenEdit({ show: false, role: null })}
                >
                  Cancel
                </Button>
                {/* <Button variant="contained" className="primaryFillBtn">
                Save Changes
              </Button> */}
              </Stack>
            </Box>
          </Box>
        }
        isShow={openEdit.show}
        callback={() => setOpenEdit({ show: false, role: null })}
      />
    </div>
  );
};

export default Roles;
