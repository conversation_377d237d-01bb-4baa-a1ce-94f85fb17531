import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
  Chip,
  InputAdornment,
  useMediaQuery,
  useTheme
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { Formik, Form } from "formik";
import * as yup from "yup";

interface PrimaryCategoryServicesProps {
  open: boolean;
  onClose: () => void;
  categoryName: string;
  isPrimary: boolean;
  onAddService: (serviceName: string) => void;
}

const PrimaryCategoryServices: React.FC<PrimaryCategoryServicesProps> = ({
  open,
  onClose,
  categoryName,
  isPrimary,
  onAddService,
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [showCustomServiceInput, setShowCustomServiceInput] = useState(false);
  const [customServiceName, setCustomServiceName] = useState("");
  const [error, setError] = useState("");
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  // Sample predefined services for eye care
  const predefinedServices = [
    "Cataract care",
    "Dry-eye treatment",
    "Emergency care",
    "Eye exams",
    "Eye procedures",
    "Frame fittings",
    "Glaucoma care",
    "Lens & contact prescriptions",
    "Macular degeneration care",
    "Online eyecare booking",
    "Strabismus squint",
    "Vision therapy",
    "Vitrectomies"
  ];

  const handleAddCustomService = () => {
    setShowCustomServiceInput(true);
  };

  const handleCancelCustomService = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
  };

  const handleToggleService = (service: string) => {
    if (selectedServices.includes(service)) {
      setSelectedServices(selectedServices.filter(s => s !== service));
    } else {
      setSelectedServices([...selectedServices, service]);
    }
  };

  const handleSubmitCustomService = () => {
    if (!customServiceName.trim()) {
      setError("Please enter a service name");
      return;
    }
    onAddService(customServiceName);
    setCustomServiceName("");
    setError("");
    setShowCustomServiceInput(false);
  };

  const handleSave = () => {
    // Add all selected services
    selectedServices.forEach(service => {
      onAddService(service);
    });
    
    // If custom service is being added, add it too
    if (showCustomServiceInput && customServiceName.trim()) {
      onAddService(customServiceName);
      setCustomServiceName("");
      setShowCustomServiceInput(false);
    }
    
    // Reset state and close modal
    setSelectedServices([]);
    onClose();
  };

  // Validation schema
  const serviceSchema = yup.object({
    serviceName: yup
      .string()
      .max(120, "Service name must be at most 120 characters")
  });

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={fullScreen}
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxHeight: "90vh"
        }
      }}
    >
      <AppBar 
        position="relative" 
        color="transparent" 
        elevation={0}
        sx={{ 
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          bgcolor: "white"
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={onClose}
            aria-label="back"
            sx={{ color: "black" }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" }
            }}
          >
            Add services
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            aria-label="more"
            sx={{ color: "black", mr: 1 }}
          >
            <MoreVertIcon />
          </IconButton>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            sx={{ color: "black" }}
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent sx={{ p: 2, bgcolor: "white" }}>
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: "1rem", sm: "1.1rem" },
              color: "black",
              wordBreak: "break-word",
            }}
          >
            {categoryName}
          </Typography>
          <Typography
            variant="caption"
            sx={{ 
              fontSize: { xs: "0.7rem", sm: "0.75rem" },
              color: "white",
              bgcolor: "#1976d2",
              px: 1,
              py: 0.5,
              borderRadius: 1,
              display: "inline-block"
            }}
          >
            {isPrimary ? "Primary category" : "Additional category"}
          </Typography>
        </Box>

        <Typography
          variant="body1"
          sx={{
            fontSize: { xs: "0.9rem", sm: "1rem" },
            color: "black",
            mb: 2,
            mt: 3
          }}
        >
          Add services you offer and get discovered by customers
        </Typography>

        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 4 }}>
          {predefinedServices.map((service) => (
            <Chip
              key={service}
              icon={<AddIcon />}
              label={service}
              variant={selectedServices.includes(service) ? "filled" : "outlined"}
              onClick={() => handleToggleService(service)}
              sx={{ 
                borderRadius: "20px", 
                px: 1,
                my: 0.5,
                color: selectedServices.includes(service) ? "white" : "#1976d2",
                bgcolor: selectedServices.includes(service) ? "#1976d2" : "transparent",
                borderColor: "#e0e0e0",
                "& .MuiChip-icon": {
                  color: selectedServices.includes(service) ? "white" : "#1976d2",
                }
              }}
            />
          ))}
        </Box>

        <Box sx={{ mt: 4, mb: 2 }}>
          <Typography
            variant="body2"
            sx={{ 
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              color: "black"
            }}
          >
            Don't see a service you offer? Create your own
          </Typography>
        </Box>

        {showCustomServiceInput ? (
          <Box sx={{ mb: 2 }}>
            <TextField
              autoFocus
              margin="dense"
              fullWidth
              variant="outlined"
              value={customServiceName}
              onChange={(e) => {
                // Limit to 120 characters
                if (e.target.value.length <= 120) {
                  setCustomServiceName(e.target.value);
                  if (e.target.value.trim()) {
                    setError("");
                  }
                }
              }}
              error={!!error}
              helperText={error || `${customServiceName.length}/120`}
              placeholder="Enter service name"
              InputProps={{
                style: { color: "black" },
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleCancelCustomService}
                      edge="end"
                      sx={{
                        color: "black",
                        p: { xs: 0.5, sm: 1 },
                      }}
                    >
                      <CancelIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "rgba(0, 0, 0, 0.23)",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(0, 0, 0, 0.23)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#1976d2",
                  },
                },
                "& .MuiFormHelperText-root": {
                  color: error ? "#d32f2f" : "rgba(0, 0, 0, 0.6)",
                },
              }}
            />
          </Box>
        ) : (
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddCustomService}
            sx={{
              color: "#1976d2",
              textTransform: "none",
              justifyContent: "flex-start",
              padding: "8px 0",
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              minWidth: "auto",
              "& .MuiButton-startIcon": {
                mr: { xs: 0.5, sm: 1 },
              },
            }}
          >
            Add custom service
          </Button>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          p: { xs: 1.5, sm: 2 },
          justifyContent: "space-between",
          borderTop: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        <Button
          onClick={onClose}
          sx={{
            color: "#1976d2",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            "&:hover": {
              backgroundColor: "rgba(25, 118, 210, 0.08)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={selectedServices.length === 0 && !(showCustomServiceInput && customServiceName.trim())}
          sx={{
            bgcolor: "#1976d2",
            color: "white",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            px: { xs: 2, sm: 3 },
            "&:hover": {
              bgcolor: "#1565c0",
            },
            "&.Mui-disabled": {
              bgcolor: "rgba(25, 118, 210, 0.3)",
              color: "rgba(255, 255, 255, 0.7)",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PrimaryCategoryServices;