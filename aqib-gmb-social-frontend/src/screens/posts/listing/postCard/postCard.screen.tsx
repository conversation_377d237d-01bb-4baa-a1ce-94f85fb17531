import { useContext, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Card from "@mui/material/Card";
import { CardContent, Grid } from "@mui/material";
import { CardMedia, IconButton } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import CancelRoundedIcon from "@mui/icons-material/CancelRounded";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ConfirmModel from "../../../../components/confirmModel/confirmModel.component";
import { LocalPost } from "../../../../interfaces/request/IGoogleCreatePostResponse";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import Dialog from "@mui/material/Dialog";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import PostsService from "../../../../services/posts/posts.service";
import { useDispatch, useSelector } from "react-redux";
import { IDeletePostResponseModel } from "../../../../interfaces/response/IDeletePostResponseModel";
import { ToastContext } from "../../../../context/toast.context";
import { ToastSeverity } from "../../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../../constants/message.constant";
import { LoadingContext } from "../../../../context/loading.context";
import RemoveRedEyeOutlinedIcon from "@mui/icons-material/RemoveRedEyeOutlined";
import ApplicationHelperService from "../../../../services/ApplicationHelperService";
import { useNavigate } from "react-router-dom";
import { IGoogleCreatePost } from "../../../../interfaces/request/IGoogleCreatePost";

const PostCard = (props: { post: LocalPost; refreshData: () => void }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { post, refreshData } = props;
  const _postsService = new PostsService(dispatch);
  const [showMaximized, setShowMaximized] = useState<boolean>(false);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const [showConfirmDelete, setShowConfirmDelete] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const deletePost = async () => {
    setShowConfirmDelete(false);
    setLoading(true);
    const deleteResponse: IDeletePostResponseModel =
      await _postsService.deletePost(userInfo.id, post.name);

    if (deleteResponse.isSuccess) {
      setToastConfig(
        ToastSeverity.Success,
        MessageConstants.PostDeletedSuccessfully,
        true
      );
    } else {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.UnableToDeletePostAtThisMoment,
        true
      );
    }
    refreshData();
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % post.media.length);
  };

  const handlePrev = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + post.media.length) % post.media.length
    );
  };

  const CardContentView = () => {
    const _applicationHelperService = new ApplicationHelperService({});

    return (
      <Card
        className="managementPostCard"
        sx={{
          boxShadow: 3,
          minHeight: 450,
          overflow: "hidden",
          borderRadius: 2,
        }}
      >
        <Box>
          {showMaximized && (
            <IconButton
              style={{ position: "absolute", zIndex: 99, right: 0 }}
              color="primary"
              onClick={() => setShowMaximized(false)}
            >
              <CancelRoundedIcon />
            </IconButton>
          )}
        </Box>
        {/* {post.label && (
          <Chip
            label={post.label}
            sx={{
              position: "absolute",
              top: 10,
              left: 10,
              backgroundColor: "black",
              color: "white",
              fontSize: 12,
            }}
          />
        )} */}

        <CardMedia
          component="div"
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
          }}
        >
          {!showMaximized && (
            <Box className="labelTag">
              {_applicationHelperService.toTitleCase(post.topicType)}
            </Box>
          )}

          <img
            src={post.media[currentIndex].googleUrl}
            alt={`Image ${currentIndex + 1}`}
            style={{
              width: "100%",
              height: "300px",
              objectFit: "fill",
              borderRadius: "8px",
              transition: "opacity 0.5s ease-in-out",
            }}
            referrerPolicy="no-referrer"
          />

          {/* Previous Button */}
          {post.media.length > 1 && currentIndex > 0 && (
            <IconButton
              onClick={handlePrev}
              sx={{
                position: "absolute",
                left: 10,
                backgroundColor: "rgba(0,0,0,0.5)",
                color: "white",
                "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
              }}
            >
              <ArrowBackIos />
            </IconButton>
          )}

          {/* Next Button */}
          {post.media.length > 1 && currentIndex < post.media.length && (
            <IconButton
              onClick={handleNext}
              sx={{
                position: "absolute",
                right: 10,
                backgroundColor: "rgba(0,0,0,0.5)",
                color: "white",
                "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
              }}
            >
              <ArrowForwardIos />
            </IconButton>
          )}
        </CardMedia>

        {/* {post.media && post.media.length > 0 ? (
          <div
            style={{
              display: "flex",
              overflowX: "auto",
              scrollbarWidth: "none",
              msOverflowStyle: "none",
              scrollBehavior: "smooth",
            }}
            className="scroll-container"
          >
            {post.media.map((src, index) => (
              <img
                key={index}
                src={src.googleUrl}
                alt={`image-${index}`}
                style={{ width: "100%", flexShrink: 0 }}
              />
            ))}
          </div>
        ) : (
          <Box
            sx={{
              height: 180,
              backgroundColor: "#f4f4f4",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography variant="body2" color="textSecondary">
              No Image Available
            </Typography>
          </Box>
        )} */}

        <CardContent
          onClick={() =>
            showMaximized
              ? console.log("Already in Maximized View")
              : setShowMaximized(true)
          }
        >
          <Grid container spacing={2}>
            <Grid xs={10}></Grid>
            <Grid xs={2} className="columnEnd">
              {post.state === "LIVE" ? (
                <ToggleOnIcon
                  sx={{
                    color: "green",
                    fontSize: 30,
                  }}
                />
              ) : (
                <ToggleOffIcon
                  sx={{
                    color: "red",
                    fontSize: 30,
                  }}
                />
              )}
            </Grid>
          </Grid>

          {/* This should be Togeather */}
          <>
            {post.event && (
              <Typography variant="subtitle1" fontWeight="bold">
                {post.event.title}
              </Typography>
            )}

            <Typography
              variant="body2"
              color="textSecondary"
              className={showMaximized ? "" : "minified-content"}
            >
              {post.summary}
            </Typography>
          </>

          <Grid container spacing={2} marginTop={2}>
            <Grid xs={6} className="paddingLeft">
              <Typography>
                <span className="commonLabel">Created On:</span>
              </Typography>
              <Typography>
                <span className="commonInput">
                  {_applicationHelperService.getExpandedDateTimeFormat(
                    post.createTime
                  )}
                </span>
              </Typography>
            </Grid>
            <Grid xs={6}>
              <Typography>
                <span className="commonLabel">Updated On:</span>
              </Typography>
              <Typography>
                <span className="commonInput">
                  {" "}
                  {_applicationHelperService.getExpandedDateTimeFormat(
                    post.updateTime
                  )}
                </span>
              </Typography>
            </Grid>
          </Grid>

          {/* {post.button ? (
            <Button
              variant="contained"
              startIcon={<PhoneIcon />}
              sx={{ backgroundColor: "#6a0dad", color: "#fff", mt: 1 }}
            >
              Call Now
            </Button>
          ) : (
            <Typography variant="body2" sx={{ mt: 1, color: "gray" }}>
              No Button
            </Typography>
          )} */}

          {/* <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Available On: 1 Location
          </Typography> */}
        </CardContent>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-around",
            p: 1,
          }}
        >
          <IconButton
            onClick={() => window.open(post.searchUrl, "_blank")}
            color="default"
          >
            <RemoveRedEyeOutlinedIcon />
          </IconButton>
          {Boolean(rbAccess && rbAccess.PostsEdit) && (
            <IconButton
              color="primary"
              onClick={() => {
                const stateData: IGoogleCreatePost = {
                  languageCode: post.languageCode,
                  summary: post.summary,
                  callToAction: post.callToAction,
                  event: post.event,
                  offer: post.offer,
                  media: [],
                  topicType: post.topicType,
                };

                if (post.media.length > 0) {
                  for (let index = 0; index < post.media.length; index++) {
                    const element = post.media[index];

                    stateData.media.push({
                      mediaFormat: element.mediaFormat,
                      sourceUrl: element.googleUrl,
                    });
                  }
                }

                navigate("/post-management/create-social-post", {
                  state: {
                    createPost: stateData,
                    title: "Edit Post",
                  },
                });
              }}
            >
              <EditIcon />
            </IconButton>
          )}

          {Boolean(rbAccess && rbAccess.PostsDelete) && (
            <IconButton
              color="error"
              onClick={() => setShowConfirmDelete(true)}
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Box>

        <ConfirmModel
          isOpen={showConfirmDelete}
          title="Delete Post"
          description="Are you certain you want to delete this post? This action is irreversible."
          confirmText="Delete"
          cancelText="Cancel"
          cancelCallback={() => setShowConfirmDelete(false)}
          confirmCallback={() => deletePost()}
        />
      </Card>
    );
  };

  return (
    <Box>
      <CardContentView />

      <Dialog
        maxWidth={"sm"}
        open={showMaximized}
        onClose={() => setShowMaximized(false)}
      >
        <CardContentView />
      </Dialog>
    </Box>
  );
};

export default PostCard;
