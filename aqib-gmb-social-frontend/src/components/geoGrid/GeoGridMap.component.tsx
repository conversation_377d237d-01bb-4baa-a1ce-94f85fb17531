import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Stack,
} from "@mui/material";
import {
  LocationOn as LocationOnIcon,
  GridOn as GridOnIcon,
  ZoomIn as ZoomInIcon,
} from "@mui/icons-material";
import { GridPoint } from "../../services/geoGrid/geoGrid.service";

interface GeoGridMapProps {
  center: { lat: number; lng: number } | null;
  gridPoints: GridPoint[];
  loading: boolean;
}

const GeoGridMap: React.FC<GeoGridMapProps> = ({
  center,
  gridPoints,
  loading,
}) => {
  const [mapError, setMapError] = useState<string | null>(null);

  // For now, we'll show a static map representation
  // In a real implementation, you would integrate with Google Maps API
  const generateStaticMapUrl = () => {
    if (!center) return null;

    const { lat, lng } = center;
    const zoom = 13;
    const size = "600x400";

    // Create markers for grid points
    let markers = `markers=color:red%7Csize:mid%7C${lat},${lng}`;

    if (gridPoints.length > 0) {
      const gridMarkers = gridPoints
        .slice(0, 20) // Limit to avoid URL length issues
        .map((point) => `${point.lat},${point.lng}`)
        .join("%7C");

      markers += `&markers=color:blue%7Csize:small%7C${gridMarkers}`;
    }

    // Note: Replace YOUR_API_KEY with actual Google Maps API key
    return `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=${zoom}&size=${size}&${markers}&key=YOUR_API_KEY`;
  };

  const renderGridVisualization = () => {
    if (!center || gridPoints.length === 0) {
      return (
        <Box
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            bgcolor: "grey.100",
            borderRadius: 1,
          }}
        >
          <GridOnIcon sx={{ fontSize: 48, color: "grey.400", mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            No Grid Generated
          </Typography>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Search for a location and generate a grid to see it here
          </Typography>
        </Box>
      );
    }

    // Simple grid visualization
    const gridSize = Math.sqrt(gridPoints.length);
    const cellSize = 40;
    const gridWidth = gridSize * cellSize;
    const gridHeight = gridSize * cellSize;

    return (
      <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
        {/* Map Header */}
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
            <LocationOnIcon color="primary" />
            <Typography variant="subtitle1">Grid Map View</Typography>
          </Stack>

          <Stack direction="row" spacing={1}>
            <Chip
              size="small"
              label={`Center: ${Number(center.lat).toFixed(4)}, ${Number(
                center.lng
              ).toFixed(4)}`}
              color="primary"
              variant="outlined"
            />
            <Chip
              size="small"
              label={`${gridPoints.length} Points`}
              color="secondary"
              variant="outlined"
            />
          </Stack>
        </Box>

        {/* Static Map or Grid Visualization */}
        <Box
          sx={{
            flex: 1,
            position: "relative",
            bgcolor: "grey.50",
            borderRadius: 1,
            overflow: "hidden",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {/* Static Map Image */}
          <Box
            component="img"
            src={generateStaticMapUrl() || ""}
            alt="Grid Map"
            sx={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              display: generateStaticMapUrl() ? "block" : "none",
            }}
            onError={() => setMapError("Failed to load map")}
          />

          {/* Fallback Grid Visualization */}
          {(mapError || !generateStaticMapUrl()) && (
            <Box
              sx={{
                position: "relative",
                width: gridWidth,
                height: gridHeight,
                border: "2px solid",
                borderColor: "primary.main",
                borderRadius: 1,
              }}
            >
              {/* Grid Lines */}
              <svg
                width={gridWidth}
                height={gridHeight}
                style={{ position: "absolute", top: 0, left: 0 }}
              >
                {/* Vertical lines */}
                {Array.from({ length: gridSize + 1 }, (_, i) => (
                  <line
                    key={`v-${i}`}
                    x1={i * cellSize}
                    y1={0}
                    x2={i * cellSize}
                    y2={gridHeight}
                    stroke="#ccc"
                    strokeWidth={1}
                  />
                ))}
                {/* Horizontal lines */}
                {Array.from({ length: gridSize + 1 }, (_, i) => (
                  <line
                    key={`h-${i}`}
                    x1={0}
                    y1={i * cellSize}
                    x2={gridWidth}
                    y2={i * cellSize}
                    stroke="#ccc"
                    strokeWidth={1}
                  />
                ))}
              </svg>

              {/* Grid Points */}
              {gridPoints.map((point, index) => {
                const row = Math.floor(index / gridSize);
                const col = index % gridSize;
                const x = col * cellSize + cellSize / 2;
                const y = row * cellSize + cellSize / 2;
                const isCenter =
                  row === Math.floor(gridSize / 2) &&
                  col === Math.floor(gridSize / 2);

                return (
                  <Box
                    key={index}
                    sx={{
                      position: "absolute",
                      left: x - 6,
                      top: y - 6,
                      width: 12,
                      height: 12,
                      borderRadius: "50%",
                      bgcolor: isCenter ? "error.main" : "primary.main",
                      border: "2px solid white",
                      boxShadow: 1,
                      cursor: "pointer",
                      "&:hover": {
                        transform: "scale(1.2)",
                        zIndex: 1,
                      },
                    }}
                    title={`Point ${index + 1}: ${Number(point.lat).toFixed(
                      4
                    )}, ${Number(point.lng).toFixed(4)}`}
                  />
                );
              })}

              {/* Center marker */}
              <Box
                sx={{
                  position: "absolute",
                  left: gridWidth / 2 - 8,
                  top: gridHeight / 2 - 8,
                  width: 16,
                  height: 16,
                  borderRadius: "50%",
                  bgcolor: "error.main",
                  border: "3px solid white",
                  boxShadow: 2,
                  zIndex: 2,
                }}
                title={`Center: ${Number(center.lat).toFixed(4)}, ${Number(
                  center.lng
                ).toFixed(4)}`}
              />
            </Box>
          )}

          {/* Loading overlay */}
          {loading && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: "rgba(255, 255, 255, 0.8)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: 10,
              }}
            >
              <CircularProgress />
            </Box>
          )}
        </Box>

        {/* Map Footer */}
        {mapError && (
          <Alert severity="warning" sx={{ mt: 1 }}>
            {mapError}. Showing simplified grid visualization.
          </Alert>
        )}

        <Box sx={{ mt: 1, textAlign: "center" }}>
          <Typography variant="caption" color="text.secondary">
            🔴 Center Point • 🔵 Grid Points
          </Typography>
        </Box>
      </Box>
    );
  };

  return <Box sx={{ height: "100%" }}>{renderGridVisualization()}</Box>;
};

export default GeoGridMap;
