import React from "react";
import { <PERSON>, Typography, Tooltip, Chip } from "@mui/material";
import {
  LocationOn as LocationOnIcon,
  Business as BusinessIcon,
} from "@mui/icons-material";
import { GridPoint } from "../../services/geoGrid/geoGrid.service";

interface LocationMarkersProps {
  gridPoints: GridPoint[];
  centerPoint: { lat: number; lng: number } | null;
  onMarkerClick?: (point: GridPoint) => void;
  showLabels?: boolean;
}

const LocationMarkers: React.FC<LocationMarkersProps> = ({
  gridPoints,
  centerPoint,
  onMarkerClick,
  showLabels = false,
}) => {
  const getMarkerColor = (point: GridPoint, index: number) => {
    // Center point gets a different color
    if (
      centerPoint &&
      Math.abs(point.lat - centerPoint.lat) < 0.0001 &&
      Math.abs(point.lng - centerPoint.lng) < 0.0001
    ) {
      return "error.main";
    }

    // Alternate colors for grid points
    return index % 2 === 0 ? "primary.main" : "secondary.main";
  };

  const formatCoordinates = (lat: number, lng: number) => {
    return `${Number(lat).toFixed(6)}, ${Number(lng).toFixed(6)}`;
  };

  const getMarkerSize = (point: GridPoint) => {
    // Center point is larger
    if (
      centerPoint &&
      Math.abs(point.lat - centerPoint.lat) < 0.0001 &&
      Math.abs(point.lng - centerPoint.lng) < 0.0001
    ) {
      return { width: 16, height: 16 };
    }
    return { width: 12, height: 12 };
  };

  if (!gridPoints || gridPoints.length === 0) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          p: 3,
          textAlign: "center",
        }}
      >
        <LocationOnIcon sx={{ fontSize: 48, color: "grey.400", mb: 1 }} />
        <Typography variant="body1" color="text.secondary">
          No location markers to display
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Generate a grid to see location markers
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ position: "relative", width: "100%", height: "100%" }}>
      {/* Grid Points */}
      {gridPoints.map((point, index) => {
        const markerSize = getMarkerSize(point);
        const isCenter =
          centerPoint &&
          Math.abs(point.lat - centerPoint.lat) < 0.0001 &&
          Math.abs(point.lng - centerPoint.lng) < 0.0001;

        return (
          <Tooltip
            key={index}
            title={
              <Box>
                <Typography variant="body2">
                  {isCenter ? "Center Point" : `Grid Point ${index + 1}`}
                </Typography>
                <Typography variant="caption">
                  {formatCoordinates(point.lat, point.lng)}
                </Typography>
                {point.address && (
                  <Typography variant="caption" display="block">
                    {point.address}
                  </Typography>
                )}
              </Box>
            }
            arrow
          >
            <Box
              sx={{
                position: "absolute",
                left: `${((point.lng + 180) / 360) * 100}%`,
                top: `${((90 - point.lat) / 180) * 100}%`,
                transform: "translate(-50%, -50%)",
                cursor: onMarkerClick ? "pointer" : "default",
                zIndex: isCenter ? 10 : 5,
                "&:hover": {
                  transform: "translate(-50%, -50%) scale(1.2)",
                  zIndex: 20,
                },
              }}
              onClick={() => onMarkerClick && onMarkerClick(point)}
            >
              {/* Marker Icon */}
              <Box
                sx={{
                  width: markerSize.width,
                  height: markerSize.height,
                  borderRadius: "50%",
                  bgcolor: getMarkerColor(point, index),
                  border: "2px solid white",
                  boxShadow: 2,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {isCenter ? (
                  <BusinessIcon sx={{ fontSize: 8, color: "white" }} />
                ) : (
                  <LocationOnIcon sx={{ fontSize: 6, color: "white" }} />
                )}
              </Box>

              {/* Label */}
              {showLabels && (
                <Chip
                  label={isCenter ? "Center" : `P${index + 1}`}
                  size="small"
                  sx={{
                    position: "absolute",
                    top: -30,
                    left: "50%",
                    transform: "translateX(-50%)",
                    fontSize: "0.6rem",
                    height: 20,
                    bgcolor: "background.paper",
                    border: "1px solid",
                    borderColor: "divider",
                  }}
                />
              )}
            </Box>
          </Tooltip>
        );
      })}

      {/* Legend */}
      <Box
        sx={{
          position: "absolute",
          bottom: 10,
          left: 10,
          bgcolor: "background.paper",
          p: 1,
          borderRadius: 1,
          boxShadow: 1,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="caption" display="block" gutterBottom>
          Legend:
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mb: 0.5 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: "50%",
              bgcolor: "error.main",
              border: "1px solid white",
            }}
          />
          <Typography variant="caption">Center Point</Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Box
            sx={{
              width: 6,
              height: 6,
              borderRadius: "50%",
              bgcolor: "primary.main",
              border: "1px solid white",
            }}
          />
          <Typography variant="caption">Grid Points</Typography>
        </Box>
      </Box>

      {/* Grid Info */}
      <Box
        sx={{
          position: "absolute",
          top: 10,
          right: 10,
          bgcolor: "background.paper",
          p: 1,
          borderRadius: 1,
          boxShadow: 1,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="caption" display="block">
          Total Points: {gridPoints.length}
        </Typography>
        {centerPoint && (
          <Typography variant="caption" display="block">
            Center: {formatCoordinates(centerPoint.lat, centerPoint.lng)}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default LocationMarkers;
