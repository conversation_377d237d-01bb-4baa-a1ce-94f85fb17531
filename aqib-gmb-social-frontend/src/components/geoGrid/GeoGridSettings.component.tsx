import React from "react";
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  FormControlLabel,
  Switch,
  Button,
  Divider,
  <PERSON>,
  Stack,
} from "@mui/material";
import { LoadingButton } from "@mui/lab";
import {
  GridOn as GridOnIcon,
  Schedule as ScheduleIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { GridConfiguration } from "../../services/geoGrid/geoGrid.service";

interface GeoGridSettingsProps {
  configuration: GridConfiguration;
  onConfigurationChange: (updates: Partial<GridConfiguration>) => void;
  onGenerateGrid: () => void;
  loading: boolean;
}

const GeoGridSettings: React.FC<GeoGridSettingsProps> = ({
  configuration,
  onConfigurationChange,
  onGenerateGrid,
  loading,
}) => {
  const gridSizeOptions = ["3x3", "4x4", "5x5", "6x6"];
  const distanceUnitOptions = [
    { value: "meters", label: "Meters" },
    { value: "kilometers", label: "Kilometers" },
    { value: "miles", label: "Miles" },
  ];

  const handleGridSizeChange = (gridSize: string) => {
    onConfigurationChange({ gridSize });
  };

  const handleDistanceChange = (distance: number) => {
    onConfigurationChange({ distance });
  };

  const handleDistanceUnitChange = (distanceUnit: "meters" | "kilometers" | "miles") => {
    onConfigurationChange({ distanceUnit });
  };

  const handleScheduleToggle = (isScheduleEnabled: boolean) => {
    onConfigurationChange({ isScheduleEnabled });
  };

  const getGridPointCount = (gridSize: string) => {
    const size = parseInt(gridSize.split('x')[0]);
    return size * size;
  };

  const getEstimatedArea = () => {
    const gridSizeNum = parseInt(configuration.gridSize.split('x')[0]);
    const distance = configuration.distance;
    const unit = configuration.distanceUnit;
    
    // Calculate total area covered by the grid
    const totalDistance = distance * (gridSizeNum - 1);
    const area = totalDistance * totalDistance;
    
    return { area: area.toFixed(2), unit: unit === "meters" ? "m²" : unit === "kilometers" ? "km²" : "mi²" };
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <SettingsIcon />
        Grid Settings
      </Typography>

      {/* Grid Size Configuration */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Grid Size</InputLabel>
        <Select
          value={configuration.gridSize}
          label="Grid Size"
          onChange={(e) => handleGridSizeChange(e.target.value)}
        >
          {gridSizeOptions.map((size) => (
            <MenuItem key={size} value={size}>
              {size} ({getGridPointCount(size)} points)
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Distance Configuration */}
      <TextField
        fullWidth
        label="Distance Between Points"
        type="number"
        value={configuration.distance}
        onChange={(e) => handleDistanceChange(parseFloat(e.target.value) || 0)}
        sx={{ mb: 2 }}
        inputProps={{ min: 1, step: 1 }}
      />

      {/* Distance Unit */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Distance Unit</InputLabel>
        <Select
          value={configuration.distanceUnit}
          label="Distance Unit"
          onChange={(e) => handleDistanceUnitChange(e.target.value as any)}
        >
          {distanceUnitOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Grid Information */}
      <Box sx={{ mb: 2, p: 2, bgcolor: "grey.50", borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          Grid Information
        </Typography>
        <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
          <Chip
            size="small"
            icon={<GridOnIcon />}
            label={`${getGridPointCount(configuration.gridSize)} Points`}
            color="primary"
            variant="outlined"
          />
        </Stack>
        <Typography variant="body2" color="text.secondary">
          Estimated Coverage: {getEstimatedArea().area} {getEstimatedArea().unit}
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Schedule Configuration */}
      <Typography variant="h6" gutterBottom sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <ScheduleIcon />
        Schedule Settings
      </Typography>

      <FormControlLabel
        control={
          <Switch
            checked={configuration.isScheduleEnabled}
            onChange={(e) => handleScheduleToggle(e.target.checked)}
          />
        }
        label="Enable Schedule Check"
        sx={{ mb: 2 }}
      />

      {configuration.isScheduleEnabled && (
        <Box sx={{ mb: 2, p: 2, bgcolor: "info.light", borderRadius: 1, color: "info.contrastText" }}>
          <Typography variant="body2">
            Schedule functionality will be available in future updates. This will allow you to automatically check grid points at specified intervals.
          </Typography>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      {/* Action Buttons */}
      <LoadingButton
        fullWidth
        variant="contained"
        onClick={onGenerateGrid}
        loading={loading}
        startIcon={<RefreshIcon />}
        disabled={!configuration.centerLat || !configuration.centerLng}
        sx={{ mb: 2 }}
      >
        Apply Settings & Regenerate
      </LoadingButton>

      {/* Quick Presets */}
      <Typography variant="subtitle2" gutterBottom>
        Quick Presets
      </Typography>
      
      <Stack direction="column" spacing={1}>
        <Button
          size="small"
          variant="outlined"
          onClick={() => {
            onConfigurationChange({
              gridSize: "3x3",
              distance: 500,
              distanceUnit: "meters",
            });
          }}
        >
          Small Area (3x3, 500m)
        </Button>
        
        <Button
          size="small"
          variant="outlined"
          onClick={() => {
            onConfigurationChange({
              gridSize: "4x4",
              distance: 1,
              distanceUnit: "kilometers",
            });
          }}
        >
          Medium Area (4x4, 1km)
        </Button>
        
        <Button
          size="small"
          variant="outlined"
          onClick={() => {
            onConfigurationChange({
              gridSize: "5x5",
              distance: 2,
              distanceUnit: "kilometers",
            });
          }}
        >
          Large Area (5x5, 2km)
        </Button>
      </Stack>
    </Box>
  );
};

export default GeoGridSettings;
