import { Formik } from "formik";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs"; // Day.js library
import {
  Box,
  Typography,
  TextField,
  FormControlLabel,
  MenuItem,
  Radio,
  RadioGroup,
  FormControl,
  Button,
} from "@mui/material";
import {
  LocalizationProvider,
  MobileDateTimePicker,
  StaticDatePicker,
  StaticTimePicker,
} from "@mui/x-date-pickers";
import { IGoogleCreatePost } from "../../interfaces/request/IGoogleCreatePost";
import { useState } from "react";
import { ISchedulePostRequestModel } from "../../interfaces/request/ISchedulePostRequestModel";
import * as yup from "yup";
import { getIn } from "formik";

const ScheduleLater = (props: {
  googleCreatePOst: IGoogleCreatePost;
  formikSchedulerRef: any;
}) => {
  const { googleCreatePOst, formikSchedulerRef } = props;
  const SchedulePostSchema = yup.object().shape({
    selectedDateTime: yup.date().required("From date is required."),
    repeatFrequency: yup.string().required("Repeat Frequency is required."),
    repeatEndOption: yup.string(),
    repeatEndDateTime: yup
      .date()
      .nullable()
      .when("$repeatEndOption", (repeatEndOption, schema) => {
        if (repeatEndOption && repeatEndOption[0] === "On") {
          return schema.nonNullable().required("Repeat End Date is required");
        }
        return schema; // Keep it nullable for other types
      }),
    occurrences: yup
      .number()
      .nullable()
      .when("$repeatEndOption", (repeatEndOption, schema) => {
        if (repeatEndOption && repeatEndOption[0] === "after") {
          return schema
            .nonNullable()
            .moreThan(0, "Occurrences must be greater than 0")
            .required("Occurrences is required");
        }
        return schema; // Keep it nullable for other types
      }),
  });

  const SCHEDULES_INITIALS: ISchedulePostRequestModel = {
    selectedDateTime: dayjs(),
    repeatFrequency: "Daily",
    repeatEndOption: "never",
    repeatEndDateTime: dayjs(),
    occurrences: 2,
  };

  const [scheduleInitials, setScheduleInitials] =
    useState<ISchedulePostRequestModel>(SCHEDULES_INITIALS);

  const handleDateChange = (
    date: dayjs.Dayjs | null,
    values: ISchedulePostRequestModel,
    setFieldValue: (field: string, value: any) => void
  ) => {
    const newDateTime = dayjs(date)
      .hour(dayjs(values.selectedDateTime).hour()) // Preserve the selected hour
      .minute(dayjs(values.selectedDateTime).minute()); // Preserve the selected minute
    setFieldValue("selectedDateTime", newDateTime);
    setFieldValue("repeatEndDateTime", newDateTime.add(1, "day"));
  };

  const handleTimeChange = (
    time: dayjs.Dayjs | null,
    values: ISchedulePostRequestModel,
    setFieldValue: (field: string, value: any) => void
  ) => {
    const newDateTime = dayjs(values.selectedDateTime)
      .hour(dayjs(time).hour()) // Update the hour
      .minute(dayjs(time).minute()); // Update the minute
    setFieldValue("selectedDateTime", newDateTime);
    setFieldValue("repeatEndDateTime", newDateTime.add(1, "day"));
  };

  // const handleValidation = (values: ISchedulePostRequestModel) => {
  // };

  return (
    <Formik
      innerRef={formikSchedulerRef}
      enableReinitialize
      initialValues={{ ...scheduleInitials }}
      validationSchema={SchedulePostSchema}
      onSubmit={(values, { setSubmitting }) => {
        console.log("Form submitted:", values);
        setSubmitting(false); // Ensure Formik does not stay in submitting state
      }}
      // validate={handleValidation}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        isSubmitting,
        isValid,
        setFieldValue,
        setFieldTouched,
        setTouched,
      }) => (
        <form
          onSubmit={(e) => {
            e.preventDefault(); // Prevents page refresh
            handleSubmit(e); // Call Formik's submit handler
          }}
          className="height100"
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", md: "row" },
              gap: 4,
              paddingBottom: 2,
            }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              sx={{ width: "100%" }}
            >
              <Box
                sx={{
                  width: "50%",
                  bgcolor: "white",
                  p: 2,
                  textAlign: "center",
                }}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <StaticDatePicker
                    displayStaticWrapperAs="desktop"
                    value={values.selectedDateTime}
                    onChange={(date) =>
                      handleDateChange(date, values, setFieldValue)
                    }
                    disablePast
                  />
                </LocalizationProvider>
              </Box>

              <Box
                sx={{
                  width: "50%",
                  bgcolor: "white",
                  p: 2,
                  textAlign: "center",
                }}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <StaticTimePicker
                    value={values.selectedDateTime}
                    onChange={(time) =>
                      handleTimeChange(time, values, setFieldValue)
                    }
                  />
                </LocalizationProvider>
              </Box>
            </Box>

            <Box sx={{ flex: 1, minWidth: "300px" }}>
              <Typography variant="h6" gutterBottom>
                Repeat Frequency
              </Typography>
              <TextField
                select
                fullWidth
                label="Select Frequency"
                name="repeatFrequency"
                value={values.repeatFrequency}
                onChange={handleChange}
                margin="normal"
                variant="outlined"
              >
                {["Daily", "Weekly", "Monthly", "Yearly"].map((freq) => (
                  <MenuItem key={freq} value={freq}>
                    {freq}
                  </MenuItem>
                ))}
              </TextField>

              <Typography variant="h6" sx={{ mt: 3 }} gutterBottom>
                Repeat Ends
              </Typography>
              <FormControl component="fieldset">
                <RadioGroup
                  row
                  name="repeatEndOption"
                  value={values.repeatEndOption}
                  onChange={handleChange}
                >
                  <FormControlLabel
                    value="never"
                    control={<Radio />}
                    label="Never"
                  />
                  <FormControlLabel
                    sx={{ marginTop: "10px" }}
                    value="after"
                    control={<Radio />}
                    label={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography>After</Typography>
                        <TextField
                          id="occurrences"
                          type="number"
                          size="small"
                          name="occurrences"
                          variant="outlined"
                          sx={{ width: 80 }}
                          value={values.occurrences}
                          onChange={handleChange}
                          disabled={values.repeatEndOption !== "after"}
                          error={Boolean(
                            getIn(errors, "occurrences") &&
                              getIn(touched, "occurrences")
                          )}
                          helperText={
                            getIn(errors, "occurrences") &&
                            getIn(touched, "occurrences")
                              ? getIn(errors, "occurrences")
                              : ""
                          }
                          InputProps={{
                            inputProps: { min: 1 }, // Enforce minimum value
                          }}
                        />
                        <Typography>occurrences</Typography>
                      </Box>
                    }
                  />
                  <FormControlLabel
                    sx={{ marginTop: "10px" }}
                    value="on"
                    control={<Radio />}
                    label={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography>On</Typography>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <MobileDateTimePicker
                            value={values.repeatEndDateTime}
                            onChange={(date) =>
                              setFieldValue("repeatEndDateTime", date)
                            }
                            disabled={values.repeatEndOption !== "on"}
                            minDate={
                              values.selectedDateTime &&
                              values.selectedDateTime != null
                                ? dayjs(values.selectedDateTime).add(1, "day")
                                : undefined
                            }
                            maxDate={dayjs(values.selectedDateTime).add(
                              6,
                              "months"
                            )}
                          />
                        </LocalizationProvider>
                      </Box>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        </form>
      )}
    </Formik>
  );
};

export default ScheduleLater;
