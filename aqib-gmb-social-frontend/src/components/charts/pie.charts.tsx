import React from "react";
import { Bar, PolarArea } from "react-chartjs-2";
import { Chart, registerables } from "chart.js";
import { Pie } from "react-chartjs-2";
Chart.register(...registerables);

export const PieChart = (props: { chartData: any }) => {
  const data = {
    labels: ["Scheduled", "Posted", "Failed"],
    datasets: [
      {
        label: "# of Posts",
        data: [200, 120, 80],
        backgroundColor: [
          "rgba(255, 206, 86, 0.2)",
          "rgba(75, 192, 192, 0.2)",
          "rgba(255, 99, 132, 0.2)",
        ],
        borderColor: [
          "rgba(255, 206, 86, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(255, 99, 132, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    maintainAspectRatio: false, // Allows custom height and width
    responsive: true,
  };

  return (
    <div className="chart-container">
      <Pie height={400} data={data} options={options} />
    </div>
  );
};
