import React from "react";
import { Bar } from "react-chartjs-2";
import { Chart, registerables } from "chart.js";
import { Card, CardContent } from "@mui/material";
Chart.register(...registerables);

export const RegisteredEmployeesChart = (props: { chartData: any }) => {
  const MONTHS = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  //   const months = (config: any) => {
  //     var cfg = config || {};
  //     var count = cfg.count || 12;
  //     var section = cfg.section;
  //     var values = [];
  //     var i, value;

  //     for (i = 0; i < count; ++i) {
  //       value = MONTHS[Math.ceil(i) % 12];
  //       values.push(value.substring(0, section));
  //     }

  //     return values;
  //   }

  //   const labels = months({count: 7});

  const data = {
    labels: ["January", "February", "March", "April", "May", "June", "July"],
    datasets: [
      {
        label: "Users",
        data: [65, 59, 80, 81, 56, 55, 40],
        backgroundColor: [
          "rgba(255, 99, 132, 0.2)",
          "rgba(255, 159, 64, 0.2)",
          "rgba(255, 205, 86, 0.2)",
          "rgba(75, 192, 192, 0.2)",
          "rgba(54, 162, 235, 0.2)",
          "rgba(153, 102, 255, 0.2)",
          "rgba(201, 203, 207, 0.2)",
        ],
        borderColor: [
          "rgb(255, 99, 132)",
          "rgb(255, 159, 64)",
          "rgb(255, 205, 86)",
          "rgb(75, 192, 192)",
          "rgb(54, 162, 235)",
          "rgb(153, 102, 255)",
          "rgb(201, 203, 207)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    maintainAspectRatio: false, // Allows custom height and width
    responsive: true,
  };

  return (
    <div className="chart-container">
      <Bar height={400} data={data} options={options} />
    </div>
  );
};
