import React from "react";
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
} from "@mui/material";
import NearMeOutlinedIcon from "@mui/icons-material/NearMeOutlined";

interface PlaceInfo {
  placeName: string;
  placeId: string;
}

interface ServiceAreaProps {
  placeInfos: PlaceInfo[];
}

const ServiceAreaList: React.FC<ServiceAreaProps> = ({ placeInfos }) => {
  const handleOpenMap = (placeId: string) => {
    const url = `https://www.google.com/maps/place/?q=place_id:${placeId}`;
    window.open(url, "_blank");
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Service Areas
      </Typography>
      <Grid container spacing={2}>
        {placeInfos.map((place, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card sx={{ height: "100%" }}>
              <CardContent>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography variant="body1" sx={{ mr: 2 }}>
                    {place.placeName}
                  </Typography>
                  {/* <Button
                    onClick={() => handleOpenMap(place.placeId)}
                    variant="outlined"
                    className="emptyBtn editIconBtn"
                    startIcon={<NearMeOutlinedIcon />}
                  /> */}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ServiceAreaList;
