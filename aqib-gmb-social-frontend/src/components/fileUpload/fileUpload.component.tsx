import React, { useState, useRef, DragEvent } from "react";
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DeleteIcon from "@mui/icons-material/Delete";
import ImageIcon from "@mui/icons-material/Image";
import VideoLibraryIcon from "@mui/icons-material/VideoLibrary";
import { FileUtils } from "../../utils/fileUtils";

interface FileUploadComponentProps {
  onFileUpload: (files: FileList) => void;
  uploading: boolean;
  maxSizeMB: number;
  currentUsageMB: number;
}

interface FileWithPreview {
  file: File;
  preview?: string;
  error?: string;
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  onFileUpload,
  uploading,
  maxSizeMB,
  currentUsageMB,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles: FileWithPreview[] = [];
    const remainingSpace =
      maxSizeMB * 1024 * 1024 - currentUsageMB * 1024 * 1024;

    Array.from(files).forEach((file) => {
      const validation = FileUtils.validateFile(file, 100); // 100MB per file limit

      if (!validation.valid) {
        newFiles.push({
          file,
          error: validation.error,
        });
        return;
      }

      // Check if adding this file would exceed storage limit
      const totalSelectedSize = selectedFiles.reduce(
        (sum, f) => sum + f.file.size,
        0
      );
      if (totalSelectedSize + file.size > remainingSpace) {
        newFiles.push({
          file,
          error: "Would exceed storage limit",
        });
        return;
      }

      // Create preview for images
      if (FileUtils.isImage(file.type)) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const fileIndex = newFiles.findIndex((f) => f.file === file);
          if (fileIndex !== -1) {
            newFiles[fileIndex].preview = e.target?.result as string;
            setSelectedFiles([...selectedFiles, ...newFiles]);
          }
        };
        reader.readAsDataURL(file);
        newFiles.push({ file });
      } else {
        newFiles.push({ file });
      }
    });

    if (newFiles.some((f) => !FileUtils.isImage(f.file.type))) {
      setSelectedFiles([...selectedFiles, ...newFiles]);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const removeFile = (index: number) => {
    const newFiles = [...selectedFiles];
    newFiles.splice(index, 1);
    setSelectedFiles(newFiles);
  };

  const handleUpload = () => {
    const validFiles = selectedFiles.filter((f) => !f.error);
    if (validFiles.length === 0) return;

    const fileList = new DataTransfer();
    validFiles.forEach((f) => fileList.items.add(f.file));

    onFileUpload(fileList.files);
    setSelectedFiles([]);
  };

  const getTotalSize = () => {
    return selectedFiles.reduce((sum, f) => sum + f.file.size, 0);
  };

  const getValidFilesCount = () => {
    return selectedFiles.filter((f) => !f.error).length;
  };

  const remainingSpaceMB = maxSizeMB - currentUsageMB;
  const canUpload = getValidFilesCount() > 0 && !uploading;

  return (
    <Card sx={{ marginBottom: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Upload Assets
        </Typography>

        {remainingSpaceMB <= 0 && (
          <Alert severity="error" sx={{ marginBottom: 2 }}>
            Storage limit reached. Please delete some files or increase storage
            limit.
          </Alert>
        )}

        {remainingSpaceMB > 0 && remainingSpaceMB < 100 && (
          <Alert severity="warning" sx={{ marginBottom: 2 }}>
            Low storage space remaining: {remainingSpaceMB.toFixed(2)} MB
          </Alert>
        )}

        <Box
          className={`upload-zone ${dragOver ? "dragover" : ""}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
          sx={{
            border: "2px dashed #ccc",
            borderRadius: 2,
            padding: 4,
            textAlign: "center",
            cursor: remainingSpaceMB > 0 ? "pointer" : "not-allowed",
            backgroundColor: dragOver ? "#e3f2fd" : "transparent",
            borderColor: dragOver ? "#1976d2" : "#ccc",
            opacity: remainingSpaceMB > 0 ? 1 : 0.5,
            transition: "all 0.3s ease",
          }}
        >
          <CloudUploadIcon
            sx={{ fontSize: 48, color: "#1976d2", marginBottom: 2 }}
          />
          <Typography variant="h6" gutterBottom>
            {remainingSpaceMB > 0
              ? "Drop files here or click to browse"
              : "Storage limit reached"}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Supported formats: JPEG, PNG, GIF, WebP, MP4, AVI, MOV, WMV, FLV,
            WebM
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Maximum file size: 100 MB per file
          </Typography>
        </Box>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*"
          onChange={handleFileInputChange}
          style={{ display: "none" }}
          disabled={remainingSpaceMB <= 0}
        />

        {selectedFiles.length > 0 && (
          <Box sx={{ marginTop: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Selected Files ({getValidFilesCount()} valid,{" "}
              {FileUtils.formatFileSize(getTotalSize())})
            </Typography>

            <List dense>
              {selectedFiles.map((fileWithPreview, index) => (
                <ListItem key={index} divider>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      flex: 1,
                    }}
                  >
                    {FileUtils.isImage(fileWithPreview.file.type) ? (
                      <ImageIcon color="primary" />
                    ) : (
                      <VideoLibraryIcon color="secondary" />
                    )}

                    <Box sx={{ flex: 1 }}>
                      <ListItemText
                        primary={fileWithPreview.file.name}
                        secondary={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <span>
                              {FileUtils.formatFileSize(
                                fileWithPreview.file.size
                              )}
                            </span>
                            <Chip
                              label={FileUtils.getFileTypeDisplay(
                                fileWithPreview.file.type
                              )}
                              size="small"
                              color={
                                FileUtils.isImage(fileWithPreview.file.type)
                                  ? "primary"
                                  : "secondary"
                              }
                            />
                            {fileWithPreview.error && (
                              <Chip
                                label={fileWithPreview.error}
                                size="small"
                                color="error"
                              />
                            )}
                          </Box>
                        }
                      />
                    </Box>

                    {fileWithPreview.preview && (
                      <Box
                        component="img"
                        src={fileWithPreview.preview}
                        alt="Preview"
                        sx={{
                          width: 50,
                          height: 50,
                          objectFit: "cover",
                          borderRadius: 1,
                        }}
                      />
                    )}
                  </Box>

                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => removeFile(index)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>

            <Box sx={{ marginTop: 2, display: "flex", gap: 2 }}>
              <Button
                variant="contained"
                onClick={handleUpload}
                disabled={!canUpload}
                startIcon={<CloudUploadIcon />}
              >
                Upload {getValidFilesCount()} File
                {getValidFilesCount() !== 1 ? "s" : ""}
              </Button>

              <Button
                variant="outlined"
                onClick={() => setSelectedFiles([])}
                disabled={uploading}
              >
                Clear All
              </Button>
            </Box>

            {uploading && (
              <Box sx={{ marginTop: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Uploading files...
                </Typography>
                <LinearProgress />
              </Box>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default FileUploadComponent;
