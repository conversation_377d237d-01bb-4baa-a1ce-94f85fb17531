import React, { useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import { TransitionProps } from "@mui/material/transitions";
import { useNavigate } from "react-router-dom";
import "../confirmModel/confirmModel.component.style.css";

const ConfirmModel = (props: {
  isOpen: boolean;
  title: string;
  description: string;
  cancelText: string;
  confirmText: string;
  confirmCallback: () => Promise<void> | undefined;
  cancelCallback: () => void | undefined;
}) => {
  const Transition = React.forwardRef(function Transition(
    props: TransitionProps & {
      children: React.ReactElement<any, any>;
    },
    ref: React.Ref<unknown>
  ) {
    return <Slide direction="up" ref={ref} {...props} />;
  });

  return (
    <React.Fragment>
      <Dialog
        open={props.isOpen}
        TransitionComponent={Transition}
        keepMounted
        onClose={props.cancelCallback}
        aria-describedby="alert-dialog-slide-description"
        fullWidth
      >
        <DialogTitle>{props.title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-slide-description">
            {props.description}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            className="updatesShapeBtn"
            variant="outlined"
            onClick={props.cancelCallback}
            color="primary"
          >
            {props.cancelText}
          </Button>
          <Button
            className="updatesShapeBtn"
            variant="contained"
            onClick={props.confirmCallback}
            color="secondary"
          >
            {props.confirmText}
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};

export default ConfirmModel;
