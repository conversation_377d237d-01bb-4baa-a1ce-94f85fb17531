import React from "react";
import {
  Box,
  Typography,
  Avatar,
  Rating,
  Container,
  CssBaseline,
} from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import StarIcon from "@mui/icons-material/Star";
import UserAvatar from "../../../../userAvatar/userAvatar.component";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";

//Css Import
import "../testimonialCard4/testimonialCard4.component.style.css";

const TestimonialCard4 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      ref={props.divRef}
      sx={{
        backgroundImage: `url(${require("../../../../../assets/feedbackBackgrouns/5.png")})`,
        backgroundColor: props.templateConfig.backgroundColor,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundBlendMode: "overlay", // Try other blend modes like "multiply" if desired
        color: "#fff",
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        border: 1,
        borderColor: "black",
      }}
    >
      <Container>
        <CssBaseline />
        <Box>
          {/* Main Content */}
          <Box className="testimonialInnerFour">
            {/* Avatar and Name */}
            <Box>
              {props.templateConfig.showAvatar && (
                <UserAvatar
                  profileImage={props.templateConfig.reviewerImage}
                  fullname={props.templateConfig.reviewerName}
                  style={{
                    width: 60,
                    height: 60,
                    margin: "0 auto 10px",
                    background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
                  }}
                />
              )}

              <Box>
                <Typography
                  className="testimonialTitle"
                  variant="h6"
                  fontWeight="bold"
                >
                  {props.templateConfig.reviewerName}
                </Typography>
              </Box>
            </Box>

            {/* Review Text */}
            <Typography
              variant="body2"
              color="text.secondary"
              mb={2}
              sx={{
                color: `${props.templateConfig.fontColor}`,
              }}
            >
              {props.templateConfig.comment}
            </Typography>
            {/* Rating */}
            <Box>
              {props.templateConfig.showRating && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    mb: 2,
                  }}
                >
                  <RatingsStar starRating={props.templateConfig.starRating} />
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default TestimonialCard4;
