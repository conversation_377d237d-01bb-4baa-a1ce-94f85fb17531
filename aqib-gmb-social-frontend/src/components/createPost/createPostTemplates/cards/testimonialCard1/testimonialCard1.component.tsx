import React from "react";
import { Box, Typography } from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";
import UserAvatar from "../../../../userAvatar/userAvatar.component";

const TestimonialCard1 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      ref={props.divRef}
      sx={{
        height: "100%",
        width: "100%",
        padding: "30px",
        // backgroundColor: `${props.templateConfig.backgroundColor}`, // Yellow background
        background: `${props.templateConfig.backgroundColor}`,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        textAlign: "center",
        boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.2)",
      }}
    >
      <Box
        sx={{
          height: "300px",
          width: "300px",
          padding: "30px",
          backgroundColor: "#ffffff",
          borderRadius: "50%",
          position: "relative",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
          boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.2)",
          overflow: "hidden",
        }}
      >
        {/* Avatar */}
        {props.templateConfig.showAvatar && (
          <UserAvatar
            profileImage={props.templateConfig.reviewerImage}
            fullname={props.templateConfig.reviewerName}
            style={{
              width: 60,
              height: 60,
              margin: "0 auto 10px",
              background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
            }}
          />
        )}

        {/* Star Rating */}
        {props.templateConfig.showRating && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              mb: 2,
            }}
          >
            <RatingsStar starRating={props.templateConfig.starRating} />
          </Box>
        )}

        {/* Testimonial Text */}
        <Typography
          variant="body1"
          sx={{
            fontSize: "14px",
            lineHeight: "1.6",
            color: `${props.templateConfig.fontColor}`,
          }}
        >
          {props.templateConfig.comment}
        </Typography>

        {/* Author Section */}
        <Typography
          variant="h6"
          sx={{
            fontSize: "16px",
            fontWeight: 600,
            marginTop: "20px",
            color: "#0056A6",
          }}
        >
          — {props.templateConfig.reviewerName}
        </Typography>
      </Box>
    </Box>
  );
};

export default TestimonialCard1;
