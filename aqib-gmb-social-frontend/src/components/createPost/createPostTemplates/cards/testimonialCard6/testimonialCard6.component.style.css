.testimonialInnerFive
{
    background-color: #ffffff1a;
    max-width: 420px;
    margin: 0 auto;
    padding: 24px;
    border-radius: 20px;
    -webkit-box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);
    -moz-box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);
    box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);
    position: relative;
}
.testimonialInnerFive .MuiAvatar-circular
{
    width: 60px;
    height: 60px;
    margin-right: 12px;
}
.testimonialInnerFive .testmonialUserInfo
{
    display: flex;
}
.testimonialInnerFive .testmonialUserName
{
    color: #00416F;
    font-size: 28px;
    font-style: italic;
    font-weight: 400;
    letter-spacing: 4px;
}
.testimonialInnerFive .testmonialUserDesignation
{
    color: #24ABF1 !important;
    font-weight: 500 !important;
}
.testimonialInnerFive .testmonialUserInfo
{
    display: flex;
    text-align: left;
}
.testimonialInnerFive .ratingIcon
{
    font-size: 28px;
}
.testimonialInnerFive .quoteMark
{
    position: absolute;
    top: -10px;
    right: 40px;
}
.testimonialInnerFive .quoteMark img
{
    width: 80px;
}