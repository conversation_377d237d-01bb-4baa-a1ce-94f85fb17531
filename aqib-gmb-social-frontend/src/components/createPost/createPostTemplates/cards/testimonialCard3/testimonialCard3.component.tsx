import React from "react";
import {
  Box,
  Typography,
  Avatar,
  Rating,
  Container,
  CssBaseline,
} from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import StarIcon from "@mui/icons-material/Star";
import UserAvatar from "../../../../userAvatar/userAvatar.component";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";

const TestimonialCard3 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      ref={props.divRef}
      sx={{
        backgroundImage: `url(${require("../../../../../assets/feedbackBackgrouns/1.jpg")})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        borderRadius: 2,
        color: "#fff",
        padding: 4,
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.3)",
      }}
    >
      <Container>
        <CssBaseline />
        <Box
          sx={{
            position: "relative",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: 4,
            borderRadius: 3,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.2)",
            backgroundColor: "#fff",
            width: 250,
            height: 250,
            margin: "auto",
          }}
        >
          {/* Background Text */}
          {/* <Typography
        variant="h1"
        sx={{
          position: "absolute",
          top: "-60px",
          left: "-20px",
          fontSize: "8rem",
          fontWeight: "bold",
          color: "rgba(0, 0, 0, 0.1)",
          zIndex: 1,
        }}
      >
        feedback
      </Typography> */}

          {/* Main Content */}
          <Box
            sx={{
              zIndex: 2,
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
            }}
          >
            {/* Avatar and Name */}
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              {/* Avatar */}
              {props.templateConfig.showAvatar && (
                <UserAvatar
                  profileImage={props.templateConfig.reviewerImage}
                  fullname={props.templateConfig.reviewerName}
                  style={{
                    width: 60,
                    height: 60,
                    margin: "0 auto 10px",
                    background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
                  }}
                />
              )}
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  {props.templateConfig.reviewerName}
                </Typography>
              </Box>
            </Box>

            {/* Star Rating */}
            {props.templateConfig.showRating && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mb: 2,
                }}
              >
                <RatingsStar starRating={props.templateConfig.starRating} />
              </Box>
            )}

            {/* Review Text */}
            <Typography variant="body2" color="text.secondary" mb={2}>
              {props.templateConfig.comment}
            </Typography>

            {/* Author Section */}
            <Typography
              variant="h6"
              sx={{
                fontSize: "16px",
                fontWeight: 600,
                marginTop: "20px",
                color: "#0056A6",
              }}
            >
              — {props.templateConfig.reviewerName}
            </Typography>
          </Box>

          {/* Floating Heart Icon */}
          <Box
            sx={{
              position: "absolute",
              top: "-20px",
              right: "-20px",
              width: 50,
              height: 50,
              backgroundColor: "#4caf50",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#fff",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)",
            }}
          >
            ❤️
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default TestimonialCard3;
