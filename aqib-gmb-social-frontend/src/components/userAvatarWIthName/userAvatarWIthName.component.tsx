import Stack from "@mui/material/Stack";
import UserAvatar from "../userAvatar/userAvatar.component";
import Box from "@mui/material/Box";

const UserAvatarWithName = (props: { fullname: string }) => {
  return (
    <Stack direction="row" spacing={2} alignItems="center">
      <Box>
        <UserAvatar fullname={props.fullname} />
      </Box>
      <Box>
        <h4>{props.fullname}</h4>
      </Box>
    </Stack>
  );
};

export default UserAvatarWithName;
