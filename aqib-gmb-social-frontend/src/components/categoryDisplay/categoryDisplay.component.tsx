import React from "react";
import {
  Card,
  CardContent,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const CategoryDisplay = (props: { categories: any }) => {
  const { primaryCategory, additionalCategories } = props.categories;

  return (
    <div>
      {/* Primary Category */}
      {primaryCategory && primaryCategory.serviceTypes && (
        <Card className="" variant="outlined" sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Primary Category: {primaryCategory.displayName}
            </Typography>

            <Accordion expanded className="commonBorderCard">
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Service Types</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
                  {primaryCategory &&
                    primaryCategory.serviceTypes &&
                    primaryCategory.serviceTypes.map((service: any) => (
                      <Chip
                        key={service.serviceTypeId}
                        label={service.displayName}
                        variant="outlined"
                      />
                    ))}
                </div>
              </AccordionDetails>
            </Accordion>

            <Accordion expanded className="commonBorderCard">
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>More Hours Types</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
                  {primaryCategory.moreHoursTypes.map((hours: any) => (
                    <Chip
                      key={hours.hoursTypeId}
                      label={hours.displayName}
                      variant="outlined"
                    />
                  ))}
                </div>
              </AccordionDetails>
            </Accordion>
          </CardContent>
        </Card>
      )}

      {/* Additional Categories */}
      {additionalCategories && (
        <Card variant="outlined" sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Additional Categories
            </Typography>
            <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
              {additionalCategories.map((category: any, idx: number) => (
                <Chip
                  key={category.displayName}
                  label={category.displayName}
                  variant="outlined"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* {additionalCategories.map((category: any, idx: number) => (
        <Card key={category.name} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6">{category.displayName}</Typography>
            <Divider sx={{ my: 1 }} />
            <List dense>
              {category.moreHoursTypes.map((hours: any) => (
                <ListItem key={hours.hoursTypeId}>
                  <ListItemText primary={hours.displayName} />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      ))} */}
    </div>
  );
};

export default CategoryDisplay;
