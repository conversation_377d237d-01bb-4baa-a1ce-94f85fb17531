import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  CircularProgress,
  Badge,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import MediaGallery from "../mediaGallery/mediaGallery.component";
import MovieIcon from "@mui/icons-material/Movie";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";

const LogoPhotoSection = (props: { mediaItems: any }) => {
  return (
    <Box>
      <Grid container spacing={2}>
        {/* Storefront Photos Section */}
        <Grid item xs={12} md={7}>
          <Card variant="outlined">
            <Box
              sx={{
                position: "relative",
                height: "100%",
              }}
            >
              <MediaGallery mediaItems={props.mediaItems} />
              {/* Green Check */}
              <CheckCircleIcon
                sx={{
                  color: "green",
                  fontSize: 36,
                  position: "absolute",
                  top: 16,
                  right: 16,
                }}
              />
            </Box>
          </Card>
        </Grid>

        {/* Right Side Section */}
        <Grid item xs={12} md={5}>
          <Grid container direction="column" spacing={2}>
            {/* Add Videos */}
            <Grid item>
              <Box
                sx={{
                  border: "1px solid #e0e0e0",
                  borderRadius: 2,
                  p: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Box>
                  <Box className="commonTabsIcon">
                    <MovieIcon />
                  </Box>
                  <Typography variant="subtitle1" fontWeight={600}>
                    Add Videos
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Engage with your Customers by adding Brand Videos & Reels
                  </Typography>
                </Box>

                <Box position="relative" display="inline-flex">
                  <CircularProgress
                    variant="determinate"
                    value={100}
                    sx={{
                      color: "#e0e0e0", // Pending color
                    }}
                    size={40}
                    thickness={4}
                  />

                  {/* Completed (foreground) */}
                  <CircularProgress
                    variant="determinate"
                    value={10}
                    sx={{
                      color: "var(--secondaryColor)", // Completed color
                      position: "absolute",
                      left: 0,
                    }}
                    size={40}
                    thickness={4}
                  />

                  {/* Optional: Center text */}
                  <Box
                    top={0}
                    left={0}
                    bottom={0}
                    right={0}
                    position="absolute"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Typography
                      variant="caption"
                      component="div"
                      color="text.secondary"
                    >
                      {`${Math.round(10)}%`}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>

            {/* Profile & Cover Photo */}
            <Grid item>
              <Box
                sx={{
                  border: "1px solid #e0e0e0",
                  borderRadius: 2,
                  p: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Box>
                  <Box className="commonTabsIcon">
                    <AccountCircleIcon />
                  </Box>
                  <Typography variant="subtitle1" fontWeight={600}>
                    Profile & Cover Photo
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Add Logo & Cover Photo in your Profile
                  </Typography>
                </Box>

                <CheckCircleIcon sx={{ color: "green", fontSize: 36 }} />
              </Box>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LogoPhotoSection;
