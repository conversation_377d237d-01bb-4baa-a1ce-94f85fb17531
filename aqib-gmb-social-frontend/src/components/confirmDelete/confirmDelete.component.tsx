import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  useTheme
} from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";

interface ConfirmDeleteComponentProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  severity?: 'warning' | 'error';
}

const ConfirmDeleteComponent: React.FC<ConfirmDeleteComponentProps> = ({
  open,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = "Delete",
  cancelText = "Cancel",
  severity = 'warning'
}) => {
  const theme = useTheme();

  const getIconColor = () => {
    switch (severity) {
      case 'error':
        return theme.palette.error.main;
      case 'warning':
      default:
        return theme.palette.warning.main;
    }
  };

  const getConfirmButtonColor = () => {
    switch (severity) {
      case 'error':
        return 'error' as const;
      case 'warning':
      default:
        return 'warning' as const;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          paddingBottom: 1
        }}
      >
        <WarningIcon
          sx={{
            color: getIconColor(),
            fontSize: 28
          }}
        />
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ paddingTop: 1 }}>
        <Typography variant="body1" color="text.secondary">
          {message}
        </Typography>
      </DialogContent>

      <DialogActions
        sx={{
          padding: 3,
          paddingTop: 2,
          gap: 1,
          justifyContent: 'space-between'
        }}
      >
        <Button
          onClick={onCancel}
          variant="outlined"
          color="inherit"
          sx={{
            minWidth: 100,
            minHeight: 50
          }}
        >
          {cancelText}
        </Button>
        
        <Button
          onClick={onConfirm}
          variant="contained"
          color={getConfirmButtonColor()}
          sx={{
            minWidth: 100,
            minHeight: 50
          }}
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDeleteComponent;
