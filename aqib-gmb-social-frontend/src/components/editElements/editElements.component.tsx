import React, { useEffect, useRef, useState } from "react";
import {
  FormControl,
  FormLabel,
  Switch,
  Select,
  MenuItem,
  TextField,
  Box,
  InputLabel,
} from "@mui/material";
import ColorPicker from "react-pick-color";
import { IPostTemplateConfig } from "../../types/IPostTemplateConfig";
var deepEqual = require("deep-equal");

const EditElements = (props: {
  templateConfig: IPostTemplateConfig;
  callBack: (
    postTemplateConfig: IPostTemplateConfig
  ) => undefined | void | null;
}) => {
  const [fontType, setFontType] = useState("Poppins");
  const [fontColor, setFontColor] = useState("#000000");
  const [isPickerOpen, setIsPickerOpen] = useState<boolean>(false);
  const pickerRef = useRef<HTMLDivElement>(null); // Ref for detecting clicks outside the picker
  const handleColorChange = (color: any) => {
    setPostTemplateConfig({
      ...postTemplateConfig,
      fontColor: color.hex,
    });
    setFontColor(color.hex);
  };

  const [postTemplateConfig, setPostTemplateConfig] =
    useState<IPostTemplateConfig>(props.templateConfig);

  useEffect(() => {
    if (!deepEqual(postTemplateConfig, props.templateConfig)) {
      props.callBack(postTemplateConfig);
    }
  }, [postTemplateConfig]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node)
      ) {
        setIsPickerOpen(false); // Close the picker if the click is outside
      }
    };

    // Add the event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up the event listener on component unmount
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const [font, setFont] = useState("Arial");

  const [fontStyle, setFontStyle] = useState("normal");

  const handleFontChange = (event: any) => {
    setFont(event.target.value);
  };

  const handleFontStyleChange = (event: any) => {
    setFontStyle(event.target.value);
  };

  return (
    <Box sx={{ padding: 2, border: "1px solid #ccc", borderRadius: 2 }}>
      <FormControl fullWidth margin="normal">
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mt={1}
        >
          <FormLabel>Show Avatar</FormLabel>
          <Switch
            checked={postTemplateConfig.showAvatar}
            onChange={(e) =>
              setPostTemplateConfig({
                ...postTemplateConfig,
                showAvatar: e.target.checked,
              })
            }
          />
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mt={1}
        >
          <FormLabel>Show Rating</FormLabel>
          <Switch
            checked={postTemplateConfig.showRating}
            onChange={(e) =>
              setPostTemplateConfig({
                ...postTemplateConfig,
                showRating: e.target.checked,
              })
            }
          />
        </Box>
      </FormControl>

      <FormControl variant="filled" fullWidth>
        <InputLabel id="font-select-label">Select Font</InputLabel>
        <Select
          labelId="font-select-label"
          value={font}
          label="Select Font"
          onChange={handleFontChange}
          fullWidth
          sx={{ backgroundColor: "var(--whiteColor)", borderRadius: "5px" }}
        >
          <MenuItem value="Arial" style={{ fontFamily: "Arial" }}>
            Arial
          </MenuItem>
          <MenuItem value="Verdana" style={{ fontFamily: "Verdana" }}>
            Verdana
          </MenuItem>
          <MenuItem value="Courier New" style={{ fontFamily: "Courier New" }}>
            Courier New
          </MenuItem>
          <MenuItem value="Georgia" style={{ fontFamily: "Georgia" }}>
            Georgia
          </MenuItem>
          <MenuItem
            value="Times New Roman"
            style={{ fontFamily: "Times New Roman" }}
          >
            Times New Roman
          </MenuItem>
        </Select>
      </FormControl>

      <FormControl fullWidth variant="filled" style={{ marginTop: 20 }}>
        <InputLabel id="font-style-select-label">Select Font Style</InputLabel>
        <Select
          labelId="font-style-select-label"
          value={fontStyle}
          label="Select Font Style"
          onChange={handleFontStyleChange}
          fullWidth
          sx={{ backgroundColor: "var(--whiteColor)", borderRadius: "5px" }}
        >
          <MenuItem value="normal">Normal</MenuItem>
          <MenuItem value="bold">Bold</MenuItem>
          <MenuItem value="italic">Italic</MenuItem>
          <MenuItem value="bold italic">Bold & Italic</MenuItem>
        </Select>
      </FormControl>

      <FormControl fullWidth margin="normal">
        <FormLabel>Font Color</FormLabel>
        <Box display="flex" alignItems="center" mt={1}>
          <Box
            sx={{
              width: 24,
              height: 24,
              backgroundColor: fontColor,
              border: "1px solid #ccc",
              marginRight: 1,
            }}
          />
          <TextField
            value={fontColor}
            onChange={(e) => setFontColor(e.target.value)}
            onFocus={() => setIsPickerOpen(true)}
          />
        </Box>
        {isPickerOpen && (
          <div ref={pickerRef}>
            <ColorPicker color={fontColor} onChange={handleColorChange} />
          </div>
        )}
      </FormControl>
    </Box>
  );
};

export default EditElements;
