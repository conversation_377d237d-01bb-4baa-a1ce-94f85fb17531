//service apis

import axios from "axios";
import { useContext } from "react";
import { LoadingContext } from "../context/loading.context";

const { setLoading } = useContext(LoadingContext);
// HEADERS
const getHeaders = (reqMethod: any, formData: any = "", directToken = "") => {
  const headers: any = {
    headers: {
      // Authorization: `Bearer ${directToken ? directToken : store.getState().App.authToken}`
      "authentication-token": `${localStorage.getItem("access_token")}`,
    },
  };
  if (reqMethod === "post") {
    headers.headers["Content-Type"] = "application/json";
    if (formData.locationId) {
      headers.headers["x-gmb-business-id"] = formData.businessId;
      headers.headers["x-gmb-account-id"] = formData.accountId;
      headers.headers["x-gmb-location-id"] = formData.locationId;
    }
    if (formData.reviewId) {
      headers.headers["x-gmb-review-id"] = formData.reviewId;
    }
    if (formData.questionId) {
      headers.headers["x-gmb-question-id"] = formData.questionId;
    }
  }
  if (reqMethod === "refresh") {
    headers.headers["x-gmb-business-id"] = formData.businessId;
    headers.headers["x-gmb-account-id"] = formData.accountId;
    if (formData.locationId) {
      headers.headers["x-gmb-location-id"] = formData.locationId;
    }
  }
  if (reqMethod === "get") {
    if (formData.locationId) {
      headers.headers["x-gmb-business-id"] = formData.businessId;
      headers.headers["x-gmb-account-id"] = formData.accountId;
      headers.headers["x-gmb-location-id"] = formData.locationId;
    }
  }
  return headers;
};

// GET METHOD
const _get = async (url: string, formData: any, directToken = "") => {
  try {
    const headers = getHeaders("get", formData, directToken);
    const res = await axios.get(url, headers);
    return { status: res.status, data: res.data };
  } catch (error: any) {
    return { status: error.response?.status, message: error.message };
  }
};

// POST METHOD
const _post = async (url: string, formData: any, directToken = "") => {
  try {
    const headers = getHeaders("post", formData, directToken);
    const res = await axios.post(url, formData, headers);
    const result = { status: res.status, data: res.data };
    return result;
  } catch (error: any) {
    return { status: error.response?.status, message: error.message };
  }
};

// DELETE METHOD
const _delete = async (url: string, directToken = "") => {
  try {
    const headers = getHeaders(directToken);
    const res = await axios.delete(url, headers);
    return { status: res.status, data: res.data };
  } catch (error: any) {
    return { status: error.response?.status, message: error.message };
  }
};

// PUT METHOD
const _put = async (url: string, formData: any, directToken = "") => {
  try {
    const headers = getHeaders(directToken);
    const requestConfig = {
      method: "put",
      url: url,
      headers: headers,
      data: formData,
    };
    const res = await axios.put(url, formData, headers);
    return { status: res.status, data: res.data };
  } catch (error: any) {
    return { status: error.response?.status, message: error.message };
  }
};

// REFRESH METHOD
const _refresh = async (url: string, formData: any, directToken = "") => {
  try {
    const headers = getHeaders("refresh", formData, directToken);
    const res = await axios.get(url, headers);
    return { status: res.status, data: res.data };
  } catch (error: any) {
    return { status: error.response?.status, message: error.message };
  }
};

export { _get, _post, _delete, _put, _refresh };
