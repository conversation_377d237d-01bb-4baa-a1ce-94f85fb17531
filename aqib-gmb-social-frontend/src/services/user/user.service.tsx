import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  ASSIGN_USER,
  CREATE_USER,
  DELETE_USER,
  EDIT_USERLOCATIONS,
  ENABLE_DISABLE_USER,
  GET_ASSIGNUSER,
  LIST_OF_LOCATIONS,
  LIST_OF_ROLE,
  LIST_OF_USERS,
  UPDATE_ROLE,
  UPDATE_PASSWORD_USER,
  UPDATE_USER_COMBINED,
} from "../../constants/endPoints.constant";
import { IUserRequestModel } from "../../interfaces/request/IUserRequestModel";
import { IEditUserLocationsRequest } from "../../interfaces/request/IEditUserLocationsRequest";
import { IPaginationModel } from "../../interfaces/IPaginationModel";
import { Action } from "redux";

class UserService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  createUser = async (user: IUserRequestModel) => {
    return await this._httpHelperService.post(`${CREATE_USER}`, user);
  };

  editUser = async (roleId: number | undefined, request: any) => {
    return await this._httpHelperService.put(
      `${UPDATE_ROLE}/${roleId}`,
      request
    );
  };

  getUsers = async (userId: number) => {
    return await this._httpHelperService.get(`${LIST_OF_USERS}/${userId}`);
  };

  getUsersPaginated = async (
    paginationModel: IPaginationModel,
    userId: number
  ) => {
    return await this._httpHelperService.get(
      `${LIST_OF_USERS}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}&userId=${userId}`
    );
  };

  deleteUser = async (id: number) => {
    return await this._httpHelperService.delete(`${DELETE_USER}/${id}`);
  };

  assignUser = async (user: any) => {
    return await this._httpHelperService.post(`${ASSIGN_USER}`, user);
  };

  getUserLocationList = async (userId: number) => {
    return await this._httpHelperService.get(`${GET_ASSIGNUSER}/${userId}`);
  };

  updateUserLocation = async (request: IEditUserLocationsRequest) => {
    return await this._httpHelperService.put(`${EDIT_USERLOCATIONS}`, request);
  };

  enableDisableUser = async (userId: number, request: any) => {
    return await this._httpHelperService.post(
      `${ENABLE_DISABLE_USER}/${userId}`,
      request
    );
  };

  updateUserPassword = async (id: number, reqBody: any) => {
    return await this._httpHelperService.put(
      `${UPDATE_PASSWORD_USER}/${id}`,
      reqBody
    );
  };

  updateUserCombined = async (reqBody: any) => {
    return await this._httpHelperService.put(
      `${UPDATE_USER_COMBINED}`,
      reqBody
    );
  };
}

export default UserService;
