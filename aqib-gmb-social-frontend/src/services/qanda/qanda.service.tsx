import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  CREATE_REVIEW_TAGS,
  GET_ALL_TAGS,
  LIST_OF_QANDA,
  REFRESH_QandA,
  REPLY_TO_QandA,
  REPLY_WITH_AI,
} from "../../constants/endPoints.constant";
import { Action } from "redux";

class QandAService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  getQA = async (userId: number, locationId: string) => {
    return await this._httpHelperService.get(LIST_OF_QANDA(userId), {
      "x-gmb-location-id": locationId,
    });
  };

  getReviewReplyFromAI = async (comment: string, rating: number) => {
    return await this._httpHelperService.post(REPLY_WITH_AI, {
      comment,
      rating,
    });
  };

  createReviewTags = async (tagName: string, userId: number) => {
    return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {
      tagName,
      createdBy: userId,
    });
  };

  getAllTags = async () => {
    return await this._httpHelperService.get(GET_ALL_TAGS);
  };

  refreshQA = async (headerObject: any) => {
    return await this._httpHelperService.get(REFRESH_QandA, headerObject);
  };

  replyQandA = async (headers: any, reqBody: any) => {
    return await this._httpHelperService.post(REPLY_TO_QandA, reqBody, headers);
  };
}

export default QandAService;
