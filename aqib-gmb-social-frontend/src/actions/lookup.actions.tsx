import { ILoginModel } from "../interfaces/request/ILoginModel";
import HttpHelperService from "../services/httpHelper.service";
import { AUTH_REQUESTED, AUTH_ERROR } from "../constants/reducer.constant";
import { Action, Dispatch } from "redux";
import ApplicationHelperService from "../services/helperService";
import {} from "../constants/endPoints.constant";

const _applicationHelperService = new ApplicationHelperService({});

export const getSample = () => async (dispatch: Dispatch<Action>) => {
  try {
    const _httpHelperService = new HttpHelperService(dispatch);
    _httpHelperService
      .get("")
      .then(async (response: any) => {
        if (response.isSuccess) {
          dispatch({
            type: "",
            payload: response.response as any[],
          });
        }
      })
      .catch((error) => {
        console.log("Look Up Actions", error);
      });
  } catch (error) {
    console.log("Look Up Actions", error);
  }
};
