import {
  TOGGLED_MENU_ITEM,
  UPDATE_MENU_STATE,
} from "../constants/reducer.constant";
import { Action, Dispatch } from "redux";

export const openMenu =
  (payload: boolean) => async (dispatch: Dispatch<Action>) => {
    dispatch({
      type: UPDATE_MENU_STATE,
      payload: payload,
    });
  };

export const toggleMenu =
  (payload: string | null) => async (dispatch: Dispatch<Action>) => {
    dispatch({
      type: TOGGLED_MENU_ITEM,
      payload: payload,
    });
  };
