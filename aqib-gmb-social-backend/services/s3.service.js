const AWS = require("aws-sdk");
const logger = require("../utils/logger");

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.APP_AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.APP_AWS_SECRET_ACCESS_KEY,
  region: process.env.APP_AWS_REGION,
});

const s3 = new AWS.S3();

/**
 * Generate S3 key for business assets
 * @param {string} businessName - Business name
 * @param {number} businessId - Business ID
 * @param {string} fileName - File name
 * @returns {string} S3 key
 */
const generateS3Key = (businessName, businessId, fileName) => {
  // Replace spaces and special characters with hyphens
  // const sanitizedBusinessName = businessName
  //   .toLowerCase()
  //   .replace(/[^a-z0-9]/g, "-")
  //   .replace(/-+/g, "-")
  //   .replace(/^-|-$/g, "");

  return `business-assets/${businessId}/${fileName}`;
};

/**
 * Generate S3 key for temporary post images
 * @param {number} userId - User ID
 * @param {string} fileName - File name
 * @returns {string} S3 key
 */
const generateTempPostImageKey = (userId, fileName) => {
  const timestamp = Date.now();
  return `temp/post-images/${userId}/${timestamp}-${fileName}`;
};

/**
 * Upload file to S3
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} s3Key - S3 key/path
 * @param {string} mimeType - File MIME type
 * @returns {Promise<Object>} Upload result
 */
const uploadFileToS3 = async (fileBuffer, s3Key, mimeType) => {
  try {
    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: mimeType,
      // Removed ACL: 'public-read' due to bucket policy restrictions
    };

    logger.info("Uploading file to S3", {
      s3Key,
      mimeType,
      bucketName: process.env.APP_AWS_S3_BUCKET,
    });

    const result = await s3.upload(params).promise();

    logger.info("File uploaded successfully to S3", {
      s3Key,
      location: result.Location,
      etag: result.ETag,
    });

    return {
      success: true,
      data: {
        s3Key: result.Key,
        s3Url: result.Location,
        etag: result.ETag,
      },
    };
  } catch (error) {
    logger.error("Error uploading file to S3", {
      s3Key,
      error: error.message,
      stack: error.stack,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Delete file from S3
 * @param {string} s3Key - S3 key/path
 * @returns {Promise<Object>} Delete result
 */
const deleteFileFromS3 = async (s3Key) => {
  try {
    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: s3Key,
    };

    logger.info("Deleting file from S3", {
      s3Key,
      bucketName: process.env.APP_AWS_S3_BUCKET,
    });

    await s3.deleteObject(params).promise();

    logger.info("File deleted successfully from S3", { s3Key });

    return {
      success: true,
      message: "File deleted successfully",
    };
  } catch (error) {
    logger.error("Error deleting file from S3", {
      s3Key,
      error: error.message,
      stack: error.stack,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get file URL from S3 (signed URL for private buckets)
 * @param {string} s3Key - S3 key/path
 * @param {number} expiresIn - URL expiration time in seconds (default: 1 hour)
 * @returns {string} Signed file URL
 */
const getFileUrl = (s3Key, expiresIn = 3600) => {
  try {
    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: s3Key,
      Expires: expiresIn,
    };

    return s3.getSignedUrl("getObject", params);
  } catch (error) {
    logger.error("Error generating signed URL", {
      s3Key,
      error: error.message,
    });
    // Fallback to direct URL (might not work if bucket is private)
    return `https://${process.env.APP_AWS_S3_BUCKET}.s3.${process.env.APP_AWS_REGION}.amazonaws.com/${s3Key}`;
  }
};

/**
 * Check if file exists in S3
 * @param {string} s3Key - S3 key/path
 * @returns {Promise<boolean>} File exists
 */
const fileExists = async (s3Key) => {
  try {
    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: s3Key,
    };

    await s3.headObject(params).promise();
    return true;
  } catch (error) {
    if (error.code === "NotFound") {
      return false;
    }
    throw error;
  }
};

/**
 * Get folder size in bytes
 * @param {string} folderPrefix - S3 folder prefix
 * @returns {Promise<number>} Total size in bytes
 */
const getFolderSize = async (folderPrefix) => {
  try {
    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Prefix: folderPrefix,
    };

    let totalSize = 0;
    let continuationToken = null;

    do {
      if (continuationToken) {
        params.ContinuationToken = continuationToken;
      }

      const result = await s3.listObjectsV2(params).promise();

      if (result.Contents) {
        totalSize += result.Contents.reduce((sum, obj) => sum + obj.Size, 0);
      }

      continuationToken = result.NextContinuationToken;
    } while (continuationToken);

    return totalSize;
  } catch (error) {
    logger.error("Error getting folder size from S3", {
      folderPrefix,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

/**
 * Upload post image to temp folder in S3
 * @param {Buffer} fileBuffer - File buffer
 * @param {number} userId - User ID
 * @param {string} fileName - Original file name
 * @param {string} mimeType - File MIME type
 * @returns {Promise<Object>} Upload result
 */
const uploadPostImageToS3 = async (fileBuffer, userId, fileName, mimeType) => {
  try {
    const s3Key = generateTempPostImageKey(userId, fileName);

    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: mimeType,
      // Removed ACL as bucket doesn't allow ACLs
      // Will rely on signed URLs for access
    };

    logger.info("Uploading post image to S3 temp folder", {
      s3Key,
      mimeType,
      userId,
      bucketName: process.env.APP_AWS_S3_BUCKET,
    });

    const result = await s3.upload(params).promise();

    logger.info("Post image uploaded successfully to S3", {
      s3Key,
      location: result.Location,
      etag: result.ETag,
    });

    // Generate a signed URL with longer expiration for Google API access
    const signedUrl = getFileUrl(result.Key, 7 * 24 * 60 * 60); // 7 days expiration for Google API

    return {
      success: true,
      data: {
        s3Key: result.Key,
        s3Url: result.Location, // Direct S3 URL (public)
        signedUrl: signedUrl, // Signed URL as fallback
        etag: result.ETag,
        fileName: fileName,
        originalName: fileName,
      },
    };
  } catch (error) {
    logger.error("Error uploading post image to S3", {
      s3Key: generateTempPostImageKey(userId, fileName),
      error: error.message,
      stack: error.stack,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

module.exports = {
  generateS3Key,
  generateTempPostImageKey,
  uploadFileToS3,
  uploadPostImageToS3,
  deleteFileFromS3,
  getFileUrl,
  fileExists,
  getFolderSize,
};
