const Role = require("../models/role.models");
const logger = require("../utils/logger");

const RoleList = (req, res) => {
  const userId = req.params.userId;
  Role.fetchAll(userId)
    .then((response) => {
      res.status(201).json({ message: "Role List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Role List Not Found!", error: error });
    });
};

const UserRoles = (req, res) => {
  const userId = req.params.userId;
  Role.fetchById(userId)
    .then((response) => {
      res.status(201).json({ message: "Role List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Role List Not Found!", error: error });
    });
};

const updateOneRole = async (req, res) => {
  const { fieldname, value } = req.body;
  const id = req.params.id;
  Role.updateById(fieldname, value, id)
    .then((result) => {
      res.status(200).json({ message: "Role updated successfully" });
    })
    .catch((err) => {
      res.status(500).json({ message: "Failed to update Role" });
    });
};

module.exports = { RoleList, updateOneRole, UserRoles };
