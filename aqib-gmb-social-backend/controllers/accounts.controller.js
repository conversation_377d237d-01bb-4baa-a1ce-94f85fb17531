const GMBModels = require("../models/gmb.models");
const logger = require("../utils/logger");
const Accounts = require("../models/accounts.models");

const AccountsList = async (req, res) => {
  logger.logControllerAction("accounts", "AccountsList", req.requestId, {
    userId: req.user?.id,
  });
  const userId = req.params.userId;
  try {
    const result = await Accounts.fetchAccounts(userId);
    return res.status(201).json({ message: "accounts fetched!", data: result });
  } catch (error) {
    logger.error("Error in AccountsList", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    return res.status(400).json({ message: "Invalid request!", error: error });
  }
};

module.exports = {
  AccountsList,
};
