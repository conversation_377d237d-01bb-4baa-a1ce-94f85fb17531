const logger = require("../utils/logger");
const GeoGrid = require("../models/geoGrid.models");
const axios = require("axios");

/**
 * Welcome endpoint for geo grid
 */
const welcome = async (req, res) => {
  try {
    logger.info("Geo Grid welcome endpoint accessed", {
      requestId: req.requestId,
      userAgent: req.get("User-Agent"),
    });

    res.status(200).json({
      message: "Welcome to Geo Grid API",
      version: "1.0.0",
      endpoints: [
        "POST /search-location - Search for location",
        "POST /generate-grid - Generate grid points",
        "GET /grid-data/:gridId - Get grid data",
        "POST /save-configuration - Save grid configuration",
        "GET /configurations/:userId - Get user configurations",
        "PUT /configuration/:gridId - Update configuration",
        "DELETE /configuration/:gridId - Delete configuration",
      ],
    });
  } catch (error) {
    logger.error("Error in geo grid welcome endpoint:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to process welcome request",
    });
  }
};

/**
 * Search for location using various methods
 */
const searchLocation = async (req, res) => {
  try {
    const { searchType, query, coordinates } = req.body;

    logger.info("Searching location", {
      requestId: req.requestId,
      searchType,
      query: query || "coordinates provided",
    });

    let locationData = null;

    switch (searchType) {
      case "name":
        locationData = await searchLocationByName(query);
        break;
      case "coordinates":
        locationData = await searchLocationByCoordinates(
          coordinates.lat,
          coordinates.lng
        );
        break;
      case "mapUrl":
        locationData = await searchLocationByMapUrl(query);
        break;
      default:
        throw new Error("Invalid search type");
    }

    res.status(200).json({
      success: true,
      data: locationData,
    });
  } catch (error) {
    logger.error("Error searching location:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to search location",
      message: error.message,
    });
  }
};

/**
 * Generate grid points based on center location and configuration
 */
const generateGrid = async (req, res) => {
  try {
    const { centerLat, centerLng, gridSize, distance, distanceUnit } = req.body;

    logger.info("Generating grid", {
      requestId: req.requestId,
      centerLat,
      centerLng,
      gridSize,
      distance,
      distanceUnit,
    });

    const gridPoints = generateGridPoints(
      centerLat,
      centerLng,
      gridSize,
      distance,
      distanceUnit
    );

    res.status(200).json({
      success: true,
      data: {
        gridPoints,
        center: { lat: centerLat, lng: centerLng },
        configuration: { gridSize, distance, distanceUnit },
      },
    });
  } catch (error) {
    logger.error("Error generating grid:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to generate grid",
      message: error.message,
    });
  }
};

/**
 * Get grid data by ID
 */
const getGridData = async (req, res) => {
  try {
    const { gridId } = req.params;
    const userId = req.user.userId;

    logger.info("Fetching grid data", {
      requestId: req.requestId,
      gridId,
      userId,
    });

    const gridConfig = await GeoGrid.getGridConfigurationById(gridId, userId);
    if (!gridConfig) {
      return res.status(404).json({
        error: "Grid configuration not found",
      });
    }

    const gridPoints = await GeoGrid.getGridPoints(gridId);

    res.status(200).json({
      success: true,
      data: {
        configuration: gridConfig,
        gridPoints,
      },
    });
  } catch (error) {
    logger.error("Error fetching grid data:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to fetch grid data",
      message: error.message,
    });
  }
};

/**
 * Save grid configuration
 */
const saveGridConfiguration = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Extract gridPoints separately and remove it from config data
    const { gridPoints, ...configurationData } = req.body;
    const configData = { ...configurationData, userId };

    logger.info("Saving grid configuration", {
      requestId: req.requestId,
      userId,
      configName: configData.name,
      hasGridPoints: !!(gridPoints && gridPoints.length > 0),
      gridPointsCount: gridPoints ? gridPoints.length : 0,
    });

    // Check if user already has 10 configurations (limit)
    let existingConfigs;
    try {
      existingConfigs = await GeoGrid.getGridConfigurations(userId);
    } catch (error) {
      // If there's a JSON parsing error, try to clean up the database first
      if (error.message && error.message.includes("not valid JSON")) {
        logger.warn("JSON parsing error detected, running cleanup...", {
          requestId: req.requestId,
        });
        try {
          await GeoGrid.cleanupInvalidSettings();
          existingConfigs = await GeoGrid.getGridConfigurations(userId);
        } catch (cleanupError) {
          logger.error("Failed to cleanup invalid settings:", cleanupError);
          throw error; // Re-throw original error
        }
      } else {
        throw error;
      }
    }

    if (existingConfigs.length >= 10) {
      return res.status(400).json({
        error: "Configuration limit reached",
        message:
          "You can only save up to 10 configurations. Please delete some existing configurations first.",
      });
    }

    const savedConfig = await GeoGrid.saveGridConfiguration(configData);

    // If grid points are provided, save them too
    if (gridPoints && gridPoints.length > 0) {
      await GeoGrid.saveGridPoints(savedConfig.id, gridPoints);
    }

    res.status(201).json({
      success: true,
      data: savedConfig,
    });
  } catch (error) {
    logger.error("Error saving grid configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to save grid configuration",
      message: error.message,
    });
  }
};

/**
 * Get all grid configurations for a user
 */
const getGridConfigurations = async (req, res) => {
  try {
    const { userId } = req.params;

    logger.info("Fetching grid configurations", {
      requestId: req.requestId,
      userId,
    });

    const configurations = await GeoGrid.getGridConfigurations(userId);

    res.status(200).json({
      success: true,
      data: configurations,
    });
  } catch (error) {
    logger.error("Error fetching grid configurations:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to fetch grid configurations",
      message: error.message,
    });
  }
};

/**
 * Update grid configuration
 */
const updateGridConfiguration = async (req, res) => {
  try {
    const { gridId } = req.params;
    const userId = req.user.userId;
    const updateData = req.body;

    logger.info("Updating grid configuration", {
      requestId: req.requestId,
      gridId,
      userId,
    });

    const updatedConfig = await GeoGrid.updateGridConfiguration(
      gridId,
      userId,
      updateData
    );

    // If grid points are provided, update them too
    if (req.body.gridPoints) {
      await GeoGrid.saveGridPoints(gridId, req.body.gridPoints);
    }

    res.status(200).json({
      success: true,
      data: updatedConfig,
    });
  } catch (error) {
    logger.error("Error updating grid configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to update grid configuration",
      message: error.message,
    });
  }
};

/**
 * Delete grid configuration
 */
const deleteGridConfiguration = async (req, res) => {
  try {
    const { gridId } = req.params;
    const userId = req.user.userId;

    logger.info("Deleting grid configuration", {
      requestId: req.requestId,
      gridId,
      userId,
    });

    await GeoGrid.deleteGridConfiguration(gridId, userId);

    res.status(200).json({
      success: true,
      message: "Grid configuration deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting grid configuration:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to delete grid configuration",
      message: error.message,
    });
  }
};

/**
 * Get location suggestions for autocomplete
 */
const getLocationSuggestions = async (req, res) => {
  try {
    const { query } = req.query;

    logger.info("Getting location suggestions", {
      requestId: req.requestId,
      query,
    });

    // This would typically use Google Places API
    // For now, returning mock data
    const suggestions = [
      {
        name: `${query} - Location 1`,
        placeId: "place1",
        lat: 40.7128,
        lng: -74.006,
      },
      {
        name: `${query} - Location 2`,
        placeId: "place2",
        lat: 40.7589,
        lng: -73.9851,
      },
    ];

    res.status(200).json({
      success: true,
      data: suggestions,
    });
  } catch (error) {
    logger.error("Error getting location suggestions:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to get location suggestions",
      message: error.message,
    });
  }
};

/**
 * Validate coordinates
 */
const validateCoordinates = async (req, res) => {
  try {
    const { lat, lng } = req.body;

    logger.info("Validating coordinates", {
      requestId: req.requestId,
      lat,
      lng,
    });

    const isValid = lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;

    res.status(200).json({
      success: true,
      data: {
        isValid,
        coordinates: { lat, lng },
      },
    });
  } catch (error) {
    logger.error("Error validating coordinates:", {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId,
    });

    res.status(500).json({
      error: "Failed to validate coordinates",
      message: error.message,
    });
  }
};

// Helper functions
const searchLocationByName = async (locationName) => {
  // This would typically use Google Places API
  // For now, returning mock data
  return {
    name: locationName,
    lat: 40.7128,
    lng: -74.006,
    address: "New York, NY, USA",
    placeId: "mock_place_id",
  };
};

const searchLocationByCoordinates = async (lat, lng) => {
  // This would typically use Google Geocoding API
  // For now, returning mock data
  return {
    name: "Location Name",
    lat: parseFloat(lat),
    lng: parseFloat(lng),
    address: "Address from coordinates",
    placeId: "mock_place_id",
  };
};

const searchLocationByMapUrl = async (mapUrl) => {
  // Extract coordinates from Google Maps URL
  const coordsMatch = mapUrl.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
  if (coordsMatch) {
    const lat = parseFloat(coordsMatch[1]);
    const lng = parseFloat(coordsMatch[2]);
    return searchLocationByCoordinates(lat, lng);
  }
  throw new Error("Invalid map URL format");
};

const generateGridPoints = (
  centerLat,
  centerLng,
  gridSize,
  distance,
  distanceUnit
) => {
  const points = [];
  const gridSizeNum = parseInt(gridSize.split("x")[0]); // e.g., "3x3" -> 3
  const distanceInKm = distanceUnit === "miles" ? distance * 1.60934 : distance;

  // Calculate the offset for each grid point
  const latOffset = distanceInKm / 111.32; // Approximate km per degree latitude
  const lngOffset =
    distanceInKm / (111.32 * Math.cos((centerLat * Math.PI) / 180)); // Adjust for longitude

  const halfGrid = Math.floor(gridSizeNum / 2);

  for (let i = 0; i < gridSizeNum; i++) {
    for (let j = 0; j < gridSizeNum; j++) {
      const latIndex = i - halfGrid;
      const lngIndex = j - halfGrid;

      const lat = centerLat + latIndex * latOffset;
      const lng = centerLng + lngIndex * lngOffset;

      points.push({
        lat: parseFloat(lat.toFixed(6)),
        lng: parseFloat(lng.toFixed(6)),
        index: i * gridSizeNum + j,
        gridPosition: { row: i, col: j },
      });
    }
  }

  return points;
};

module.exports = {
  welcome,
  searchLocation,
  generateGrid,
  getGridData,
  saveGridConfiguration,
  getGridConfigurations,
  updateGridConfiguration,
  deleteGridConfiguration,
  getLocationSuggestions,
  validateCoordinates,
};
