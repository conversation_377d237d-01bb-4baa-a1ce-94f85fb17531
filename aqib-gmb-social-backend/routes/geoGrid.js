const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  searchLocation,
  generateGrid,
  getGridData,
  saveGridConfiguration,
  getGridConfigurations,
  deleteGridConfiguration,
  updateGridConfiguration,
  getLocationSuggestions,
  validateCoordinates,
} = require("../controllers/geoGrid.controller");

// Welcome route
router.get("/", welcome);

// Search location by name, coordinates, or map URL
router.post("/search-location", isAuthenticated, searchLocation);

// Generate grid based on location and configuration
router.post("/generate-grid", isAuthenticated, generateGrid);

// Get grid data for a specific configuration
router.get("/grid-data/:gridId", isAuthenticated, getGridData);

// Save grid configuration
router.post("/save-configuration", isAuthenticated, saveGridConfiguration);

// Get all grid configurations for a user
router.get("/configurations/:userId", isAuthenticated, getGridConfigurations);

// Update grid configuration
router.put("/configuration/:gridId", isAuthenticated, updateGridConfiguration);

// Delete grid configuration
router.delete("/configuration/:gridId", isAuthenticated, deleteGridConfiguration);

// Get location suggestions for autocomplete
router.get("/location-suggestions", isAuthenticated, getLocationSuggestions);

// Validate coordinates
router.post("/validate-coordinates", isAuthenticated, validateCoordinates);

module.exports = router;
