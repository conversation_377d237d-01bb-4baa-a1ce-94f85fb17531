const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");
const googleApiService = require("../services/googleapi.service");
const gmbToken = require("../models/gmb.models");

const gmbTokenMapping = async (req, res, next) => {
  const oAuth2Client = new OAuth2Client(
    keys.web.client_id,
    keys.web.client_secret,
    keys.web.redirect_uris[0]
  );
  let refreshToken = "";
  let tokenResult;
  try {
    const businessId = req.headers["x-gmb-business-id"];
    const accountId = req.headers["x-gmb-account-id"];
    req["user"]["accountId"] = accountId;
    req["user"]["businessId"] = businessId;
    tokenResult = await gmbToken.gmb_oauth_tokens(accountId);
    if (!tokenResult || !tokenResult.length) {
      return res
        .status(400)
        .json({ error: "Google Re-authentication required!" });
    }
    refreshToken = tokenResult[0].refreshToken;
    const tokenInfo = await oAuth2Client.getTokenInfo(
      tokenResult[0].accessToken
    );
    if (tokenInfo && !isTokenExpired(tokenInfo)) {
      req["user"]["gmbToken"] = tokenResult[0].accessToken;
      return next();
    } else {
      return res.status(403).json({ error: "Forbidden" });
    }
  } catch (error) {
    if (
      tokenResult &&
      error &&
      error.response &&
      error.response.data &&
      error.response.data.error === "invalid_token"
    ) {
      if (refreshToken) {
        const { tokens } = await oAuth2Client.refreshToken(refreshToken);
        req["user"]["gmbToken"] = tokens.access_token;
        await gmbToken.InsertOAuth({
          access_token: tokens.access_token,
          refresh_token: tokenResult[0].refreshToken,
          userId: tokenResult[0].userId,
          account_id: tokenResult[0].gmbAccountId,
        });
        return next();
      }
    } else {
      req["user"]["gmbToken"] = undefined;
      console.log(`ERROR: gmbTokenMapping Middleware Is Failing ${error}`);
      return res.status(403).json({ error: "Invalid Request!" });
    }
  }
};
const isTokenExpired = (tokenInfo) => {
  const currentTime = Math.floor(Date.now() / 1000);
  return currentTime >= tokenInfo.expiry_date;
};

module.exports = { gmbTokenMapping };
