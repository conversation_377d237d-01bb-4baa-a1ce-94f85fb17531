const pool = require("../config/db");

module.exports = class QandA {
  static async fetchAll(userId, gmbLocationId, limit, offset) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === 1) {
        const results = await pool.query(
          "SELECT * FROM gmb_question_answer_activity WHERE gmbLocationId = ?",
          [gmbLocationId]
        );
        return results;
      } else if (userData[0].roleId === 2) {
        const results = await pool.query(
          "SELECT DISTINCT gq.*" +
            "FROM gmb_question_answer_activity gq " +
            "JOIN gmb_locations gl ON gl.gmbLocationId = gq.gmbLocationId " +
            "JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId " +
            "JOIN gmb_businesses_master b ON a.businessId = b.id " +
            "JOIN user_business ub ON  ub.businessId = b.id " +
            "WHERE ub.userId = ?  AND gq.gmbLocationId = ? " +
            "ORDER BY gq.id DESC " +
            "LIMIT 100",
          [userId, gmbLocationId]
        );
        return results;
      } else {
        const results = await pool.query(
          "SELECT DISTINCT gq.* " +
            "FROM gmb_question_answer_activity gq " +
            "JOIN gmb_locations gl ON gl.gmbLocationId = gq.gmbLocationId " +
            "JOIN users_gmb_locations ul ON ul.gmbLocationId = gl.gmbLocationId " +
            "WHERE ul.userId = ? AND gq.gmbLocationId = ?" +
            "ORDER BY gq.createdAt DESC LIMIT 100 ",
          [userId, gmbLocationId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
  static async postQandA(QandA) {
    try {
      for (const QandAs of QandA) {
        const result = await pool.query(
          `INSERT INTO gmb_question_answer_activity(userId,userName, gmbAccountId, gmbLocationId, gmbQuestionId, question, answer, statusId, questionCreatedTime, questionUpdateTime) VALUES (?,?,?,?,?,?,?,?,?,?) 
          ON DUPLICATE KEY UPDATE answer = VALUES(answer)`,
          [
            QandAs.userId,
            QandAs.userName,
            QandAs.accountId,
            QandAs.locationId,
            QandAs.gmbQuestionId,
            QandAs.question,
            QandAs.answer,
            QandAs.statusId,
            QandAs.createTime,
            QandAs.updateTime,
          ]
        );
      }
      return;
    } catch (error) {
      console.error("Error inserting locations: ", error);
      return error;
    }
  }
  static async replyQandA(QandA) {
    try {
      console.log("Executing SQL Query with Data: ", QandA);
      const result = await pool.query(
        `UPDATE gmb_question_answer_activity 
         SET answer = ? 
         WHERE gmbQuestionId = ? AND gmbLocationId = ?`,
        [QandA.answer, QandA.gmbQuestionId, QandA.gmbLocationId]
      );
      console.log("SQL Query Result: ", result);
      return result;
    } catch (error) {
      console.error("Error updating answer in replyQandA: ", error);
      return error;
    }
  }
};
