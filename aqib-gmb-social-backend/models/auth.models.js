const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");
const gmbToken = require("../models/gmb.models");

module.exports = class Auth {
  static async Insert(tokenData) {
    try {
      const results = await pool.query(
        "INSERT INTO access_token(userId, accessToken) VALUES (?, ?)",
        [tokenData.userId, tokenData.token]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchByEmail(credentials) {
    try {
      const rows = await pool.query("SELECT * FROM users WHERE email = ?", [
        credentials.email,
      ]);
      if (rows.length === 0) {
        return { message: "User not found.", user: null };
      }
      const user = rows[0];
      const isEqual = await bcrypt.compare(credentials.password, user.password);
      if (!isEqual) {
        return { message: "Incorrect password.", user: null };
      }
      const token = jwt.sign(
        {
          email: user.email,
          mobile: user.mobile,
          userId: user.id.toString(),
          roleId: user.roleId.toString(),
          statusId: user.statusId.toString(),
        },
        process.env.APP_JWT_SECRET_KEY,
        { expiresIn: "24h" }
      );
      const tokenData = {
        userId: user.id,
        token: token,
      };
      //   await Auth.Insert(tokenData);
      return { message: "Success", user: user, token: token };
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async authenticateGoogle(oauthToken) {
    const oAuth2Client = new OAuth2Client(
      keys.web.client_id,
      keys.web.client_secret,
      keys.web.redirect_uris[0]
    );
    try {
      await oAuth2Client.getTokenInfo(oauthToken.accessToken);
      return oauthToken;
    } catch (error) {
      if (
        oauthToken &&
        error &&
        error.response &&
        error.response.data &&
        error.response.data.error === "invalid_token"
      ) {
        const { tokens } = await oAuth2Client.refreshToken(
          oauthToken.refreshToken
        );
        await gmbToken.InsertOAuth({
          access_token: tokens.access_token,
          refresh_token: oauthToken.refreshToken,
          userId: oauthToken.userId,
          account_id: oauthToken.gmbAccountId,
        });

        return {
          ...oauthToken,
          accessToken: tokens.access_token,
        };

        // const oAuthToken = (
        //   await gmbToken.gmb_oauth_tokens(oauthToken.gmbAccountId)
        // )[0];

        // return oAuthToken;
      }
    }
  }
};
