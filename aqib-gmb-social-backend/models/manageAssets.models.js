const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class ManageAssets {
  constructor(
    businessId,
    userId,
    fileName,
    originalFileName,
    fileType,
    fileSize,
    s3Key,
    s3Url,
    mimeType,
    thumbnail = null
  ) {
    this.businessId = businessId;
    this.userId = userId;
    this.fileName = fileName;
    this.originalFileName = originalFileName;
    this.fileType = fileType;
    this.fileSize = fileSize;
    this.s3Key = s3Key;
    this.s3Url = s3Url;
    this.mimeType = mimeType;
    this.thumbnail = thumbnail;
  }

  /**
   * Insert new asset record
   * @param {Object} asset - Asset data
   * @returns {Promise<Object>} Insert result
   */
  static async Insert(asset) {
    try {
      // Check if thumbnail columns exist (for backward compatibility)
      let insertQuery = `INSERT INTO business_assets
         (business_id, user_id, file_name, original_file_name, file_type, file_size, s3_key, s3_url, mime_type`;
      let insertValues = [
        asset.businessId,
        asset.userId,
        asset.fileName,
        asset.originalFileName,
        asset.fileType,
        asset.fileSize,
        asset.s3Key,
        asset.s3Url,
        asset.mimeType,
      ];

      // Add thumbnail fields if thumbnail data exists
      if (asset.thumbnail) {
        insertQuery += `, thumbnail_s3_key, thumbnail_s3_url, thumbnail_file_name, thumbnail_size`;
        insertValues.push(
          asset.thumbnail.s3Key,
          asset.thumbnail.s3Url,
          asset.thumbnail.fileName,
          asset.thumbnail.size
        );
      }

      insertQuery += `) VALUES (${insertValues.map(() => "?").join(", ")})`;

      const results = await pool.query(insertQuery, insertValues);

      logger.info("Asset record inserted successfully", {
        assetId: results.insertId,
        businessId: asset.businessId,
        fileName: asset.fileName,
      });

      return { success: true, assetId: results.insertId, results };
    } catch (error) {
      logger.error("Database query failed in ManageAssets.Insert", {
        error: error.message,
        stack: error.stack,
        asset,
      });
      throw error;
    }
  }

  /**
   * Get assets by business ID with pagination
   * @param {number} businessId - Business ID
   * @param {number} pageNo - Page number
   * @param {number} offset - Records per page
   * @returns {Promise<Object>} Assets list with pagination
   */
  static async getAssetsByBusinessId(businessId, pageNo = 1, offset = 10) {
    try {
      // Get total count
      const countResult = await pool.query(
        "SELECT COUNT(*) as total FROM business_assets WHERE business_id = ? AND status = 'active'",
        [businessId]
      );
      const totalRecords = countResult[0].total;

      // Get paginated results
      const results = await pool.query(
        `SELECT ba.*, u.name as uploaded_by_name
         FROM business_assets ba
         LEFT JOIN users u ON ba.user_id = u.id
         WHERE ba.business_id = ? AND ba.status = 'active'
         ORDER BY ba.upload_date DESC
         LIMIT ?, ?`,
        [businessId, (pageNo - 1) * offset, offset]
      );

      return {
        success: true,
        pagination: {
          totalRecords,
          pageCount: Math.ceil(totalRecords / offset),
          currentPage: pageNo,
          recordsPerPage: offset,
        },
        assets: results,
      };
    } catch (error) {
      logger.error(
        "Database query failed in ManageAssets.getAssetsByBusinessId",
        {
          error: error.message,
          stack: error.stack,
          businessId,
          pageNo,
          offset,
        }
      );
      throw error;
    }
  }

  /**
   * Get asset by ID
   * @param {number} assetId - Asset ID
   * @returns {Promise<Object>} Asset data
   */
  static async getAssetById(assetId) {
    try {
      const results = await pool.query(
        `SELECT ba.*, u.name as uploaded_by_name, b.businessName
         FROM business_assets ba
         LEFT JOIN users u ON ba.user_id = u.id
         LEFT JOIN gmb_businesses_master b ON ba.business_id = b.id
         WHERE ba.id = ? AND ba.status = 'active'`,
        [assetId]
      );

      return results.length > 0
        ? { success: true, asset: results[0] }
        : { success: false, message: "Asset not found" };
    } catch (error) {
      logger.error("Database query failed in ManageAssets.getAssetById", {
        error: error.message,
        stack: error.stack,
        assetId,
      });
      throw error;
    }
  }

  /**
   * Delete asset (soft delete)
   * @param {number} assetId - Asset ID
   * @param {number} userId - User ID performing the delete
   * @returns {Promise<Object>} Delete result
   */
  static async deleteAsset(assetId, userId) {
    try {
      const results = await pool.query(
        "UPDATE business_assets SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = ? AND status = 'active'",
        [assetId]
      );

      if (results.affectedRows === 0) {
        return {
          success: false,
          message: "Asset not found or already deleted",
        };
      }

      logger.info("Asset soft deleted successfully", {
        assetId,
        deletedBy: userId,
      });

      return { success: true, message: "Asset deleted successfully" };
    } catch (error) {
      logger.error("Database query failed in ManageAssets.deleteAsset", {
        error: error.message,
        stack: error.stack,
        assetId,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get total file size for a business
   * @param {number} businessId - Business ID
   * @returns {Promise<number>} Total size in bytes
   */
  static async getTotalSizeByBusinessId(businessId) {
    try {
      const results = await pool.query(
        "SELECT COALESCE(SUM(file_size), 0) as total_size FROM business_assets WHERE business_id = ? AND status = 'active'",
        [businessId]
      );

      return Number(results[0].total_size) || 0;
    } catch (error) {
      logger.error(
        "Database query failed in ManageAssets.getTotalSizeByBusinessId",
        {
          error: error.message,
          stack: error.stack,
          businessId,
        }
      );
      throw error;
    }
  }

  /**
   * Get max upload size for a business
   * @param {number} businessId - Business ID
   * @returns {Promise<number>} Max upload size in MB
   */
  static async getMaxUploadSize(businessId) {
    try {
      const results = await pool.query(
        "SELECT max_upload_size_mb FROM gmb_businesses_master WHERE id = ?",
        [businessId]
      );

      return results.length > 0
        ? Number(results[0].max_upload_size_mb) || 1024
        : 1024; // Default 1GB
    } catch (error) {
      logger.error("Database query failed in ManageAssets.getMaxUploadSize", {
        error: error.message,
        stack: error.stack,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Update max upload size for a business
   * @param {number} businessId - Business ID
   * @param {number} maxSizeMB - Max size in MB
   * @returns {Promise<Object>} Update result
   */
  static async updateMaxUploadSize(businessId, maxSizeMB) {
    try {
      const results = await pool.query(
        "UPDATE gmb_businesses_master SET max_upload_size_mb = ? WHERE id = ?",
        [maxSizeMB, businessId]
      );

      if (results.affectedRows === 0) {
        return { success: false, message: "Business not found" };
      }

      logger.info("Max upload size updated successfully", {
        businessId,
        maxSizeMB,
      });

      return { success: true, message: "Max upload size updated successfully" };
    } catch (error) {
      logger.error(
        "Database query failed in ManageAssets.updateMaxUploadSize",
        {
          error: error.message,
          stack: error.stack,
          businessId,
          maxSizeMB,
        }
      );
      throw error;
    }
  }
};
