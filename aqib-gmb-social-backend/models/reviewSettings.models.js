const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class ReviewSettings {
  // Get all reply templates for a user/business
  static async getReplyTemplates(userId, businessId = null) {
    try {
      let query = `
        SELECT rt.*, brt.business_id, brt.is_active as business_template_active
        FROM reply_templates rt
        LEFT JOIN business_reply_templates brt ON rt.id = brt.template_id
        WHERE rt.created_by = ?
      `;
      let params = [userId];

      if (businessId) {
        query += ` AND (brt.business_id = ? OR brt.business_id IS NULL)`;
        params.push(businessId);
      }

      query += ` ORDER BY rt.star_rating, rt.created_at DESC`;

      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Error fetching reply templates:", error);
      throw error;
    }
  }

  // Create a new reply template
  static async createReplyTemplate(templateData) {
    try {
      const {
        userId,
        starRating,
        templateName,
        templateContent,
        isDefault,
        businessId
      } = templateData;

      // If this is set as default, unset other defaults for the same star rating
      if (isDefault) {
        await pool.query(
          `UPDATE reply_templates SET is_default = 0 
           WHERE created_by = ? AND star_rating = ?`,
          [userId, starRating]
        );
      }

      const result = await pool.query(
        `INSERT INTO reply_templates 
         (created_by, star_rating, template_name, template_content, is_default, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
        [userId, starRating, templateName, templateContent, isDefault ? 1 : 0]
      );

      // If businessId is provided, create business-template mapping
      if (businessId && result.insertId) {
        await pool.query(
          `INSERT INTO business_reply_templates (business_id, template_id, is_active, created_at)
           VALUES (?, ?, 1, NOW())`,
          [businessId, result.insertId]
        );
      }

      return result;
    } catch (error) {
      console.error("Error creating reply template:", error);
      throw error;
    }
  }

  // Update reply template
  static async updateReplyTemplate(templateId, templateData, userId) {
    try {
      const {
        starRating,
        templateName,
        templateContent,
        isDefault
      } = templateData;

      // If this is set as default, unset other defaults for the same star rating
      if (isDefault) {
        await pool.query(
          `UPDATE reply_templates SET is_default = 0 
           WHERE created_by = ? AND star_rating = ? AND id != ?`,
          [userId, starRating, templateId]
        );
      }

      const result = await pool.query(
        `UPDATE reply_templates 
         SET star_rating = ?, template_name = ?, template_content = ?, 
             is_default = ?, updated_at = NOW()
         WHERE id = ? AND created_by = ?`,
        [starRating, templateName, templateContent, isDefault ? 1 : 0, templateId, userId]
      );

      return result;
    } catch (error) {
      console.error("Error updating reply template:", error);
      throw error;
    }
  }

  // Delete reply template
  static async deleteReplyTemplate(templateId, userId) {
    try {
      // First delete business mappings
      await pool.query(
        `DELETE FROM business_reply_templates WHERE template_id = ?`,
        [templateId]
      );

      // Then delete the template
      const result = await pool.query(
        `DELETE FROM reply_templates WHERE id = ? AND created_by = ?`,
        [templateId, userId]
      );

      return result;
    } catch (error) {
      console.error("Error deleting reply template:", error);
      throw error;
    }
  }

  // Get auto-reply settings for a business
  static async getAutoReplySettings(businessId) {
    try {
      const result = await pool.query(
        `SELECT * FROM auto_reply_settings WHERE business_id = ?`,
        [businessId]
      );

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error("Error fetching auto-reply settings:", error);
      throw error;
    }
  }

  // Update auto-reply settings
  static async updateAutoReplySettings(businessId, settings) {
    try {
      const {
        isEnabled,
        enabledStarRatings,
        delayMinutes,
        onlyBusinessHours,
        businessHoursStart,
        businessHoursEnd
      } = settings;

      const result = await pool.query(
        `INSERT INTO auto_reply_settings 
         (business_id, is_enabled, enabled_star_ratings, delay_minutes, 
          only_business_hours, business_hours_start, business_hours_end, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
         ON DUPLICATE KEY UPDATE
         is_enabled = VALUES(is_enabled),
         enabled_star_ratings = VALUES(enabled_star_ratings),
         delay_minutes = VALUES(delay_minutes),
         only_business_hours = VALUES(only_business_hours),
         business_hours_start = VALUES(business_hours_start),
         business_hours_end = VALUES(business_hours_end),
         updated_at = NOW()`,
        [
          businessId,
          isEnabled ? 1 : 0,
          JSON.stringify(enabledStarRatings),
          delayMinutes,
          onlyBusinessHours ? 1 : 0,
          businessHoursStart,
          businessHoursEnd
        ]
      );

      return result;
    } catch (error) {
      console.error("Error updating auto-reply settings:", error);
      throw error;
    }
  }

  // Map template to business
  static async mapTemplateToBusinesses(templateId, businessIds, userId) {
    try {
      // First, remove existing mappings for this template
      await pool.query(
        `DELETE FROM business_reply_templates WHERE template_id = ?`,
        [templateId]
      );

      // Add new mappings
      if (businessIds && businessIds.length > 0) {
        const values = businessIds.map(businessId => [businessId, templateId, 1]);
        await pool.query(
          `INSERT INTO business_reply_templates (business_id, template_id, is_active, created_at)
           VALUES ?`,
          [values.map(v => [...v, new Date()])]
        );
      }

      return { success: true };
    } catch (error) {
      console.error("Error mapping template to businesses:", error);
      throw error;
    }
  }

  // Get template by star rating for auto-reply
  static async getTemplateForAutoReply(businessId, starRating) {
    try {
      const result = await pool.query(
        `SELECT rt.* FROM reply_templates rt
         JOIN business_reply_templates brt ON rt.id = brt.template_id
         WHERE brt.business_id = ? AND rt.star_rating = ? AND brt.is_active = 1
         AND rt.is_default = 1
         LIMIT 1`,
        [businessId, starRating]
      );

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error("Error fetching template for auto-reply:", error);
      throw error;
    }
  }
};
