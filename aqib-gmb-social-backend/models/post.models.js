const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");

module.exports = class Posts {
  static async saveSchedules(requestObj) {
    try {
      const results = await pool.query(
        "INSERT INTO access_token(userId, accessToken) VALUES (?, ?)",
        [requestObj.userId, requestObj.token]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  /**
   * Create posts table if it doesn't exist
   */
  static async createPostsTable() {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS gmb_posts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          business_id INT NOT NULL,
          location_id VARCHAR(255) NOT NULL,
          account_id VARCHAR(255) NOT NULL,
          google_post_name VARCHAR(500) NOT NULL,
          bulk_post_id VARCHAR(36) NULL,
          is_bulk_post BOOLEAN DEFAULT FALSE,
          post_content JSON NOT NULL,
          post_response JSON NOT NULL,
          summary TEXT,
          topic_type VARCHAR(50),
          language_code VARCHAR(10),
          state VARCHAR(50),
          search_url TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_business_id (business_id),
          INDEX idx_location_id (location_id),
          INDEX idx_bulk_post_id (bulk_post_id),
          INDEX idx_google_post_name (google_post_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      await pool.query(createTableQuery);
      return { success: true };
    } catch (error) {
      console.error("Error creating posts table:", error);
      throw error;
    }
  }

  /**
   * Save a post to database
   */
  static async savePost(postData) {
    try {
      const {
        userId,
        businessId,
        locationId,
        accountId,
        googlePostName,
        bulkPostId,
        isBulkPost,
        postContent,
        postResponse,
        summary,
        topicType,
        languageCode,
        state,
        searchUrl,
      } = postData;

      const query = `
        INSERT INTO gmb_posts
        (user_id, business_id, location_id, account_id, google_post_name,
         bulk_post_id, is_bulk_post, post_content, post_response, summary,
         topic_type, language_code, state, search_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        userId,
        businessId,
        locationId,
        accountId,
        googlePostName,
        bulkPostId,
        isBulkPost,
        JSON.stringify(postContent),
        JSON.stringify(postResponse),
        summary,
        topicType,
        languageCode,
        state,
        searchUrl,
      ];

      const result = await pool.query(query, values);
      return { success: true, insertId: result.insertId };
    } catch (error) {
      console.error("Error saving post:", error);
      throw error;
    }
  }

  /**
   * Get post by Google post name
   */
  static async getPostByGoogleName(googlePostName) {
    try {
      const query = `
        SELECT * FROM gmb_posts
        WHERE google_post_name = ?
      `;

      const results = await pool.query(query, [googlePostName]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error("Error getting post by Google name:", error);
      throw error;
    }
  }

  /**
   * Get posts by bulk post ID
   */
  static async getPostsByBulkId(bulkPostId) {
    try {
      const query = `
        SELECT p.*, l.gmbLocationName, l.gmbLocationId
        FROM gmb_posts p
        LEFT JOIN gmb_locations l ON p.location_id = l.gmbLocationId
        WHERE p.bulk_post_id = ?
        ORDER BY p.created_at ASC
      `;

      const results = await pool.query(query, [bulkPostId]);
      return results;
    } catch (error) {
      console.error("Error getting posts by bulk ID:", error);
      throw error;
    }
  }

  /**
   * Check if a post is part of a bulk post
   */
  static async checkBulkPostStatus(googlePostName) {
    try {
      const query = `
        SELECT
          is_bulk_post,
          bulk_post_id,
          (SELECT COUNT(*) FROM gmb_posts WHERE bulk_post_id = p.bulk_post_id) as total_posts
        FROM gmb_posts p
        WHERE google_post_name = ?
      `;

      const results = await pool.query(query, [googlePostName]);

      if (results.length > 0) {
        const post = results[0];
        return {
          isBulkPost: post.is_bulk_post,
          bulkPostId: post.bulk_post_id,
          totalPosts: post.total_posts,
        };
      }

      return null;
    } catch (error) {
      console.error("Error checking bulk post status:", error);
      throw error;
    }
  }

  /**
   * Generate a new bulk post ID
   */
  static generateBulkPostId() {
    return uuidv4();
  }
};
