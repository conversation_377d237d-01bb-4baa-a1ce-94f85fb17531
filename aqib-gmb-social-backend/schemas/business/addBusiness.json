{"type": "object", "properties": {"userId": {"type": "integer"}, "businessName": {"type": "string", "minLength": 3}, "businessEmail": {"type": "string", "pattern": "^[a-zA-Z0-9_.±]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$"}, "statusId": {"type": "integer"}, "createdBy": {"type": "integer"}, "updatedBy": {"type": "integer"}}, "required": ["userId", "businessName", "businessEmail", "statusId", "created<PERSON>y", "updatedBy"]}