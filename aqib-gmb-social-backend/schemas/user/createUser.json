{"type": "object", "properties": {"roleId": {"type": "integer"}, "name": {"type": "string", "minLength": 3}, "mobile": {"type": "string", "pattern": "^(\\+91|91|0)?[6789]\\d{9}$"}, "email": {"type": "string", "pattern": "^[a-zA-Z0-9_.±]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$"}, "mobileVerified": {"type": "integer"}, "emailVerified": {"type": "integer"}, "password": {"type": "string", "minLength": 3}, "statusId": {"type": "integer"}}, "required": ["roleId", "name", "mobile", "email", "password", "statusId"]}