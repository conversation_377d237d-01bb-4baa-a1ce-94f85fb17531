const fs = require('fs');
const path = require('path');

/**
 * Custom Logger Utility for GMB Social Backend
 * Provides structured logging with different levels and automatic log rotation
 */

class Logger {
  constructor() {
    this.logLevels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      HTTP: 3,
      DEBUG: 4
    };
    
    this.currentLevel = this.getLogLevel();
    this.logDir = path.join(__dirname, '../logs');
    this.ensureLogDirectory();
  }

  /**
   * Get log level from environment or default to INFO
   */
  getLogLevel() {
    const envLevel = process.env.APP_LOG_LEVEL || 'INFO';
    return this.logLevels[envLevel.toUpperCase()] || this.logLevels.INFO;
  }

  /**
   * Ensure logs directory exists
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Get current timestamp in ISO format
   */
  getTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Get log file path for current date
   */
  getLogFilePath(type = 'app') {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${type}-${date}.log`);
  }

  /**
   * Format log message
   */
  formatMessage(level, message, meta = {}) {
    const logEntry = {
      timestamp: this.getTimestamp(),
      level: level,
      message: message,
      environment: process.env.APP_ENV_NAME || 'development',
      ...meta
    };
    
    return JSON.stringify(logEntry) + '\n';
  }

  /**
   * Write log to file
   */
  writeToFile(level, message, meta = {}, logType = 'app') {
    if (this.logLevels[level] <= this.currentLevel) {
      const formattedMessage = this.formatMessage(level, message, meta);
      const logFile = this.getLogFilePath(logType);
      
      // Write to file
      fs.appendFileSync(logFile, formattedMessage);
      
      // Also log to console in development
      if (process.env.APP_ENV_NAME === 'DEVELOPMENT') {
        console.log(`[${level}] ${message}`, meta);
      }
    }
  }

  /**
   * Error level logging
   */
  error(message, meta = {}) {
    this.writeToFile('ERROR', message, { ...meta, stack: meta.stack || new Error().stack });
  }

  /**
   * Warning level logging
   */
  warn(message, meta = {}) {
    this.writeToFile('WARN', message, meta);
  }

  /**
   * Info level logging
   */
  info(message, meta = {}) {
    this.writeToFile('INFO', message, meta);
  }

  /**
   * HTTP level logging (for requests/responses)
   */
  http(message, meta = {}) {
    this.writeToFile('HTTP', message, meta, 'http');
  }

  /**
   * Debug level logging
   */
  debug(message, meta = {}) {
    this.writeToFile('DEBUG', message, meta);
  }

  /**
   * Log request details
   */
  logRequest(req, requestId) {
    const requestData = {
      requestId: requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      headers: this.sanitizeHeaders(req.headers),
      query: req.query,
      params: req.params,
      body: this.sanitizeBody(req.body),
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id || 'anonymous'
    };

    this.http('Incoming Request', requestData);
  }

  /**
   * Log response details
   */
  logResponse(res, requestId, responseTime) {
    const responseData = {
      requestId: requestId,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('Content-Length') || 0
    };

    this.http('Outgoing Response', responseData);
  }

  /**
   * Log controller action
   */
  logControllerAction(controller, action, requestId, data = {}) {
    const actionData = {
      requestId: requestId,
      controller: controller,
      action: action,
      ...data
    };

    this.info('Controller Action', actionData);
  }

  /**
   * Log database operations
   */
  logDatabase(operation, table, requestId, data = {}) {
    const dbData = {
      requestId: requestId,
      operation: operation,
      table: table,
      ...data
    };

    this.debug('Database Operation', dbData);
  }

  /**
   * Sanitize headers to remove sensitive information
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'authentication-token', 'cookie', 'x-api-key'];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Sanitize request body to remove sensitive information
   */
  sanitizeBody(body) {
    if (!body || typeof body !== 'object') return body;
    
    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'accessToken', 'refreshToken'];
    
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Clean old log files (keep last 30 days)
   */
  cleanOldLogs() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    try {
      const files = fs.readdirSync(this.logDir);
      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < thirtyDaysAgo) {
          fs.unlinkSync(filePath);
          this.info('Cleaned old log file', { file: file });
        }
      });
    } catch (error) {
      this.error('Failed to clean old logs', { error: error.message });
    }
  }
}

// Create singleton instance
const logger = new Logger();

// Clean old logs on startup
logger.cleanOldLogs();

module.exports = logger;
