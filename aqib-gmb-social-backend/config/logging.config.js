/**
 * Logging Configuration for GMB Social Backend
 * Centralized configuration for all logging settings
 */

module.exports = {
  // Log levels: ERROR, WARN, INFO, HTTP, DEBUG
  logLevel: process.env.APP_LOG_LEVEL || 'INFO',
  
  // Environment settings
  environment: process.env.APP_ENV_NAME || 'development',
  
  // Log file settings
  logFiles: {
    app: 'app',           // General application logs
    http: 'http',         // HTTP request/response logs
    error: 'error',       // Error logs only
    database: 'database', // Database operation logs
    auth: 'auth',         // Authentication logs
    gmb: 'gmb'           // GMB API interaction logs
  },
  
  // Log retention (in days)
  retentionDays: 30,
  
  // Console logging settings
  console: {
    enabled: process.env.APP_ENV_NAME === 'DEVELOPMENT',
    colorize: true,
    timestamp: true
  },
  
  // File logging settings
  file: {
    enabled: true,
    maxSize: '10MB',
    maxFiles: 5,
    datePattern: 'YYYY-MM-DD'
  },
  
  // Sensitive fields to redact from logs
  sensitiveFields: [
    'password',
    'token',
    'secret',
    'key',
    'accessToken',
    'refreshToken',
    'authorization',
    'authentication-token',
    'cookie',
    'x-api-key',
    'client_secret',
    'private_key'
  ],
  
  // Request logging settings
  request: {
    // Log request body (be careful with sensitive data)
    logBody: process.env.APP_LOG_LEVEL === 'DEBUG',
    
    // Log response body (be careful with large responses)
    logResponseBody: process.env.APP_LOG_LEVEL === 'DEBUG',
    
    // Maximum body size to log (in characters)
    maxBodySize: 1000,
    
    // Skip logging for these paths
    skipPaths: [
      '/health',
      '/favicon.ico',
      '/robots.txt'
    ],
    
    // Skip logging for these user agents
    skipUserAgents: [
      'HealthChecker',
      'ELB-HealthChecker'
    ]
  },
  
  // Database logging settings
  database: {
    logQueries: process.env.APP_LOG_LEVEL === 'DEBUG',
    logSlowQueries: true,
    slowQueryThreshold: 1000, // milliseconds
    logErrors: true
  },
  
  // GMB API logging settings
  gmb: {
    logRequests: true,
    logResponses: process.env.APP_LOG_LEVEL === 'DEBUG',
    logErrors: true,
    logTokenRefresh: true
  },
  
  // Performance monitoring
  performance: {
    logSlowRequests: true,
    slowRequestThreshold: 5000, // milliseconds
    logMemoryUsage: process.env.APP_LOG_LEVEL === 'DEBUG',
    memoryCheckInterval: 60000 // milliseconds
  },
  
  // Error tracking
  error: {
    logStackTrace: true,
    logUserContext: true,
    logRequestContext: true,
    notifyOnCritical: false // Set to true to enable notifications
  }
};
