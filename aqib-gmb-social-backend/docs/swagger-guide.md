# Swagger Documentation Guide

## Overview

This project uses Swagger (OpenAPI) for API documentation. Swagger provides a way to describe your API using a common language that is readable by both humans and machines.

## Accessing Swagger UI

The Swagger UI is available at:

```
http://localhost:3000/api-docs
```

This interactive documentation allows you to:
- Browse all available API endpoints
- See required parameters and response formats
- Test API endpoints directly from the browser

## Authentication in Swagger UI

For endpoints that require authentication:

1. Click the "Authorize" button at the top of the Swagger UI
2. Enter your JWT token in the format: `your-token-here` (without the `Bearer` prefix)
3. Click "Authorize" to save
4. Now you can access protected endpoints

## Adding Documentation to New Endpoints

To document new endpoints, add JSDoc-style comments above your route definitions:

```javascript
/**
 * @swagger
 * /v1/your-endpoint:
 *   get:
 *     summary: Brief description
 *     description: Detailed description
 *     tags: [YourTag]
 *     parameters:
 *       - in: query
 *         name: paramName
 *         schema:
 *           type: string
 *         description: Parameter description
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 property:
 *                   type: string
 */
router.get('/your-endpoint', yourController);
```

## Defining Models/Schemas

You can define reusable schemas in the `config/swagger.js` file:

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     YourModel:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique identifier
 *         name:
 *           type: string
 *           description: Name of the item
 *       required:
 *         - name
 */
```

Then reference them in your endpoint documentation:

```javascript
/**
 * @swagger
 * /v1/your-endpoint:
 *   post:
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/YourModel'
 */
```

## Best Practices

1. Group related endpoints under the same tag
2. Provide clear summaries and descriptions
3. Document all parameters, request bodies, and responses
4. Include example values where helpful
5. Document error responses

## Resources

- [Swagger Documentation](https://swagger.io/docs/)
- [OpenAPI Specification](https://swagger.io/specification/)
- [swagger-jsdoc Documentation](https://github.com/Surnet/swagger-jsdoc)
